services:
  mongo:
    image: mongo:8.0.4
    restart: always
    ports:
      - "27017:27017"
  minio:
    image: minio/minio:RELEASE.2025-02-03T21-03-04Z
    entrypoint: sh
    command: -c 'mkdir -p /data/payload && /usr/bin/docker-entrypoint.sh minio server /data --console-address ":9001"'
    environment:
      - MINIO_ACCESS_KEY=C1FBFD0D-1CAD-4796-8883-C0FE2B0F7317
      - MINIO_SECRET_KEY=D04259D5-1BAB-4615-BE2A-B79A2B01B6AA
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - "minio_volume:/data"
  typesense:
    image: "typesense/typesense:28.0"
    command: --data-dir /data --api-key=root
    ports:
      - "8108:8108"
    volumes:
      - "typesense_volume:/data"
  valkey:
    image: valkey/valkey:8.0
    ports:
      - "6379:6379"
    volumes:
      - "valkey_volume:/data"
volumes:
  minio_volume: null
  typesense_volume: null
  valkey_volume: null
