version: 2
updates:
  - package-ecosystem: npm
    directory: /
    versioning-strategy: increase
    # We're using roughly one thousand packages in our monorepo (in total with transitive dependencies).
    # If this limit is too low, we're never really "up to date" with our dependencies.
    open-pull-requests-limit: 15
    schedule:
      interval: weekly
      day: monday
      time: "02:00"
      timezone: Europe/Berlin
    labels:
      - dependencies
    # Dependabot limits the commit messages to 15 characters.
    # Those messages fit exactly in this requirement :)
    commit-message:
      prefix: "build(deps)"
      prefix-development: "build(deps/dev)"
    ignore:
      # There seems to be a strange but within newer versions of Turbo.
      # Therefore, we skip newer versions for now.
      - dependency-name: "turbo"
      # Only create updates for Major and Minor versions for now.
      # Security updates are handled by Dependabot's security updates (even for patch versions).
      - dependency-name: "*"
        update-types: ["version-update:semver-patch"]

    # We're bundling together some related packages into groups.
    # This allows us to update all packages in a group at once.
    # See https://docs.github.com/en/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file#groups
    groups:
      formatting-linting-related:
        patterns:
          - "eslint*" # Matches eslint, eslint-plugin-*, eslint-config-*, etc.
          - "@commitlint/*" # Matches @commitlint/cli, @commitlint/config-conventional, etc.
          - "husky"
          - "lint-staged"
          - "prettier"
          - "prettier-plugin-*" # Matches prettier-plugin-tailwindcss, etc.
          - "sort-package-json"
          - "@abinnovision/eslint-config-*" # Custom ESLint configs
          - "@abinnovision/prettier-config" # Custom Prettier config
          - "@abinnovision/commitlint-config" # Custom Commitlint config

      typescript-related:
        patterns:
          - "typescript"
          - "@typescript-eslint/*"
          - "ts-loader"
          - "ts-node"
          - "tsconfig-paths"
          - "@types/node"
          - "@types/*" # All TypeScript type definitions

      build-tooling-related:
        patterns:
          - "turbo"
          - "@next/bundle-analyzer"
          - "@vitejs/*"
          - "globals"

      next-related:
        patterns:
          - "next*" # Matches next, next-auth, next-safe, etc.
          - "@next/*"
          - "@sentry/nextjs" # Often part of Next.js integration
          - "next-intl"
          - "server-only"

      react-related:
        patterns:
          - "react*" # Matches react, react-dom, react-dual-listbox, etc.
          - "@types/react*" # Matches @types/react, @types/react-dom, etc.
          - "clsx"
          - "classnames"
          - "formik"
          - "headless-stepper"
          - "@headlessui/*"
          - "@heroicons/*"
          - "usehooks-ts"
          - "@tanstack/react-query"
          - "@tanstack/react-table"
          - "react-animate-height"
          - "react-countup"
          - "react-svg-worldmap"
          - "react-use"

      payloadcms-related:
        patterns:
          - "payload"
          - "@payloadcms/*" # All PayloadCMS packages
          - "@atelier-disko/payload-lexical-react-renderer"
          - "@zapal/payload-lexical-react"
          - "graphql" # Often used with PayloadCMS

      radix-ui-related:
        patterns:
          - "@radix-ui/*" # All Radix UI components

      css-related:
        patterns:
          - "autoprefixer"
          - "postcss"
          - "tailwindcss"
          - "@tailwindcss/*" # Matches @tailwindcss/forms, @tailwindcss/typography, etc.
          - "sass"
          - "tailwind-variants"

      animation-ui-related:
        patterns:
          - "motion"
          - "lucide"
          - "lucide-react"
          - "keen-slider"

      utility-libraries:
        patterns:
          - "date-fns"
          - "deepmerge-ts"
          - "i18n-iso-countries"
          - "path-to-regexp"
          - "schema-dts"
          - "traverse"
          - "zod"
          - "plaiceholder"
          - "sharp"

      data-processing-related:
        patterns:
          - "cheerio"
          - "domhandler"
          - "sanitize-html"
          - "slug"
          - "isomorphic-unfetch"

      cache-redis-related:
        patterns:
          - "@neshca/cache-handler"
          - "iovalkey"

      search-related:
        patterns:
          - "typesense"

      testing-related:
        patterns:
          - "vitest*" # Matches vitest, vitest-mock-express, vitest-mock-extended, etc.
          - "@vitest/*"
          - "cypress"
          - "testcontainers"
          - "@testing-library/*"
          - "supertest"
          - "@types/supertest"
          - "jsdom"
          - "@types/jsdom"
          - "sentry-testkit"
          - "wait-for-expect"
