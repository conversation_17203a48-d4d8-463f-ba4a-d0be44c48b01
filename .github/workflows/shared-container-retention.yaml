on:
  workflow_call:
    inputs:
      image_name:
        required: true
        type: string
jobs:
  cleanup:
    runs-on: ubuntu-latest
    steps:
      - name: Cleanup non-release images
        uses: snok/container-retention-policy@v2
        with:
          account-type: org
          token: ${{ secrets.GITHUB_TOKEN }}
          token-type: github-token
          org-name: hansainvest-real
          image-names: ${{ inputs.image_name }}
          cut-off: one week ago UTC
          keep-at-least: 2
          filter-tags: "sha-*"
          skip-tags: "*.*.*, cache-latest"
      - name: Cleanup release images
        uses: snok/container-retention-policy@v2
        with:
          account-type: org
          token: ${{ secrets.GITHUB_TOKEN }}
          token-type: github-token
          org-name: hansainvest-real
          image-names: ${{ inputs.image_name }}
          cut-off: four week ago UTC
          keep-at-least: 5
          filter-tags: "*.*.*"
          skip-tags: "cache-latest"
      - name: Cleanup untagged images
        uses: snok/container-retention-policy@v2
        with:
          account-type: org
          token: ${{ secrets.GITHUB_TOKEN }}
          token-type: github-token
          org-name: hansainvest-real
          image-names: ${{ inputs.image_name }}
          cut-off: one day ago UTC
          keep-at-least: 2
          untagged-only: true
