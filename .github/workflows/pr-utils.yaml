# Contains different utility checks for Pull Requests.
name: PR Utils

# Only apply to PRs which target 'main' branch.
on:
  pull_request:
    branches:
      - main
    types:
      - opened
      - edited
      - synchronize

jobs:
  lint-commits:
    name: <PERSON><PERSON> commits
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - name: Run commitlint
        uses: abinnovision/actions@run-commitlint-v1
  dependabot:
    name: "Dependabot automations"
    runs-on: ubuntu-latest
    if: ${{ github.actor == 'dependabot[bot]' }}
    permissions:
      pull-requests: write
      issues: write
      repository-projects: write
      contents: write
    steps:
      - uses: actions/checkout@v4
      - id: status
        name: Resolve current status
        run: |
          echo "::set-output name=HAS_REVIEWS::$(if [[ $(gh pr status --json reviews | jq '.currentBranch.reviews | length') -eq '0' ]];then echo 'false'; else echo 'true'; fi)"
          echo "::set-output name=LAST_REVIEW_DISMISSED::$(if [[ $(gh pr status --json reviews | jq '.currentBranch.reviews[-1].state' | sed 's/"//g') -eq 'DISMISSED' ]];then echo 'true'; else echo 'false'; fi)"
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
      - name: Metadata
        id: metadata
        uses: dependabot/fetch-metadata@v1.6.0
        with:
          github-token: "${{ secrets.GITHUB_TOKEN }}"
      - name: Enable auto-rebase
        id: enable-auto-rebase
        run: gh pr merge --auto --rebase "$PR_URL"
        env:
          PR_URL: ${{github.event.pull_request.html_url}}
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
      - name: "Approve (minor & patch)"
        id: approve
        if: (steps.status.outputs.HAS_REVIEWS == 'false' || steps.status.outputs.LAST_REVIEW_DISMISSED == 'true') && steps.metadata.outputs.update-type == 'version-update:semver-minor' || steps.metadata.outputs.update-type == 'version-update:semver-patch'
        run: gh pr review --approve "$PR_URL"
        env:
          PR_URL: ${{github.event.pull_request.html_url}}
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
