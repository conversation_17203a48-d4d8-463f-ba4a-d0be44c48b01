name: Build

on:
  pull_request_target:
    branches:
      - main
  push:
    branches:
      - main

env:
  DOCKER_REGISTRY: ghcr.io

jobs:
  configure:
    name: Configure
    runs-on: ubuntu-latest
    timeout-minutes: 4
    outputs:
      commit-sha: ${{ env.COMMIT_SHA }}
    steps:
      # We first need to evaluate the commit SHA to check out.
      # If the event is a pull request (target), we need to use the head of the PR. The 'pull_request_target' event runs in the
      # context of the base branch, but we need to check out the head of the PR.
      # If the event is a push, we can use the SHA of the commit that triggered the event.
      - name: Evaluate commit
        run: |
          if [[ "${{ github.event_name }}" == "pull_request_target" ]]; then
              echo "PR is #${{ github.event.number }}..."
              echo "PR Head SHA is ${{ github.event.pull_request.head.sha }}..."
              echo "COMMIT_SHA=${{ github.event.pull_request.head.sha }}" >> $GITHUB_ENV
          else
              echo "Head SHA is ${{ github.sha }}..."
              echo "COMMIT_SHA=${{ github.sha }}" >> $GITHUB_ENV
          fi
  check:
    name: Check
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs:
      - configure
    env:
      # Avoid installing Cypress binary in this job, because it's not being used.
      CYPRESS_INSTALL_BINARY: "0"
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ needs.configure.outputs.commit-sha }}
      - name: Enable corepack
        run: corepack enable
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: ".tool-versions"
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable
      - name: Check
        run: yarn run check
      - name: Check Dependencies
        run: |
          yarn dedupe
          git diff --exit-code --quiet yarn.lock || (echo "yarn.lock is not up to date, run 'yarn dedupe'" && exit 1)
  build:
    name: Build
    runs-on: ubuntu-latest
    timeout-minutes: 15
    env:
      # Avoid installing Cypress binary in this job, because it's not being used.
      CYPRESS_INSTALL_BINARY: "0"
    needs:
      - configure
      - check
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ needs.configure.outputs.commit-sha }}
      - name: Enable corepack
        run: corepack enable
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: ".tool-versions"
          cache: "yarn"
      - name: Setup Turbo cache
        uses: rharkor/caching-for-turbo@v1.8
      - name: Install dependencies
        run: yarn install --immutable
      - name: Build
        run: yarn build
  test-unit:
    name: Test (Unit)
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs:
      - configure
      - build
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ needs.configure.outputs.commit-sha }}
      - name: Enable corepack
        run: corepack enable
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: ".tool-versions"
          cache: "yarn"
      - name: Setup Turbo cache
        uses: rharkor/caching-for-turbo@v1.8
      - name: Install dependencies
        run: yarn install --immutable
      - name: Build
        run: yarn build
      # Add when there are unit tests.
      # - name: Test
      #   run: yarn test-unit
  # Run 'release-please' to process the release workflow.
  release:
    name: Release
    runs-on: ubuntu-latest
    timeout-minutes: 15
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    outputs:
      releases_created: ${{ steps.release.outputs.releases_created }}
      paths_released: ${{ steps.release.outputs.paths_released }}
    needs:
      - configure
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - id: token
        name: "Generate token"
        uses: tibdex/github-app-token@v2
        with:
          app_id: ${{ secrets.APP_RELEASER_APP_ID }}
          private_key: ${{ secrets.APP_RELEASER_PK }}
      - id: release
        name: Run release-please
        uses: google-github-actions/release-please-action@v3
        with:
          token: ${{ steps.token.outputs.token }}
          command: "manifest"
          release-type: "node"
  build-image_apps-website:
    name: Build Image / apps/website
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs:
      - configure
      - build
      - test-unit
      - release
    permissions:
      contents: read
      packages: write
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ needs.configure.outputs.commit-sha }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Container Registry Auth
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Build image name
        run: |
          REPOSITORY_NAME_SANITIZED=$(echo "${{ github.repository }}-website" | tr '[:upper:]' '[:lower:]');
          echo "DOCKER_IMAGE_NAME=ghcr.io/$REPOSITORY_NAME_SANITIZED" >> $GITHUB_ENV
          echo "PACKAGE_NAME=$(echo "${GITHUB_REPOSITORY#*/}-website" | tr '[:upper:]' '[:lower:]')" >> $GITHUB_ENV
      - name: Evaluate version
        run: echo "RELEASE_VERSION=$(cat apps/abiconsulting/package.json | jq -r ".version")" > $GITHUB_ENV
      - name: Extract Metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_IMAGE_NAME }}
          tags: |
            type=sha,enable=true
            # Version tag is only needed for released versions.
            type=semver,pattern={{version}},value=v${{ env.RELEASE_VERSION }},enable=${{ contains(fromJSON(needs.release.outputs.paths_released), 'apps/website') }}
      - name: Evaluate BUILD_VERSION
        run: |
          # If a version has been released, use the version from the release.
          if [[ "${{ contains(fromJSON(needs.release.outputs.paths_released), 'apps/website') }}" == "true" ]]; then
            echo "BUILD_VERSION=v$RELEASE_VERSION-$(git rev-parse --short HEAD)" >> $GITHUB_ENV
          else
            # Otherwise, use the current commit hash.
            echo "BUILD_VERSION=v0.0.0-$(git rev-parse --short HEAD)" >> $GITHUB_ENV
          fi

          # Store the commit hash in the BUILD_COMMIT environment variable.
          echo "BUILD_COMMIT=$(git rev-parse HEAD)" >> $GITHUB_ENV
      # Build the image and push it to the Container registry.
      - name: Build image
        uses: docker/build-push-action@v5
        with:
          push: true
          context: "."
          file: apps/website/Dockerfile
          cache-from: type=registry,ref=${{ env.DOCKER_IMAGE_NAME }}:cache-latest
          cache-to: type=registry,ref=${{ env.DOCKER_IMAGE_NAME }}:cache-latest,mode=max
          labels: ${{ steps.meta.outputs.labels }}
          tags: ${{ steps.meta.outputs.tags }}
          build-args: |
            build_version=${{ env.BUILD_VERSION }}
            build_commit=${{ env.BUILD_COMMIT }}
      - name: Setup output directory
        run: mkdir -p output
      # Create the next.js app Values.yaml to later push to a config repository
      - name: Create Values.yaml
        run: |
          IMAGE_TAG=$(echo -e "${{ steps.meta.outputs.tags }}" | { read first_line; echo $first_line; })
          echo 'next:' > output/Values.yaml
          echo '  container:' >> output/Values.yaml
          echo "    image: \"$IMAGE_TAG\"" >> output/Values.yaml
      # Update the current tag in a remote repository to trigger the
      # deployment pipeline. In case this workflow was triggered by a release,
      # push to the main branch. Otherwise stagings
      - name: Update tags in remote repository
        uses: cpina/github-action-push-to-another-repository@v1.7.2
        env:
          SSH_DEPLOY_KEY: ${{ secrets.SSH_DEPLOY_KEY }}
        with:
          source-directory: "output"
          destination-github-username: "hansainvest-real"
          destination-repository-name: "HIRA_Web_Config"
          target-branch: ${{ contains(fromJSON(needs.release.outputs.paths_released), 'apps/website') && 'main' || 'staging' }}
          target-directory: "helm/applications/sicore-home/next/"
  cleanup-registry-configuration:
    name: Cleanup Registry (configuration)
    runs-on: ubuntu-latest
    timeout-minutes: 1
    outputs:
      package-website: ${{ steps.names.outputs.PACKAGE_NAME_WEBSITE }}
    needs:
      - configure
      - build-image_apps-website
    steps:
      - id: names
        name: Package names
        run: |
          echo "PACKAGE_NAME_WEBSITE=$(echo "${GITHUB_REPOSITORY#*/}-website" | tr '[:upper:]' '[:lower:]')" >> $GITHUB_OUTPUT
  cleanup-registry-matrix:
    name: Cleanup Registry (${{ matrix.image }})
    needs:
      - configure
      - cleanup-registry-configuration
      - build-image_apps-website
    strategy:
      matrix:
        image:
          - "${{ needs.cleanup-registry-configuration.outputs.package-website }}"
    uses: ./.github/workflows/shared-container-retention.yaml
    with:
      image_name: ${{ matrix.image }}
