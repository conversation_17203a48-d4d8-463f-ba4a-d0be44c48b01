{"$schema": "https://json.schemastore.org/package.json", "name": "@internal/root", "version": "0.0.0", "private": true, "license": "proprietary", "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "yarn turbo build", "check": "yarn format:check && yarn sort:check && turbo run check", "fix": "yarn format:fix && yarn sort:fix && turbo run fix", "format:check": "prettier --check '{.github/**/*,*}.{json,json5,yaml,yml,md}'", "format:fix": "prettier --write '{.github/**/*,*}.{json,json5,yaml,yml,md}'", "postinstall": "husky", "sort:check": "sort-package-json --check", "sort:fix": "sort-package-json", "start:dev": "yarn turbo watch start:dev", "test-integration": "vitest --run --coverage --project \\*#integration", "test-unit": "vitest --run --coverage --project \\*#unit"}, "commitlint": {"extends": ["@abinnovision/commitlint-config"]}, "lint-staged": {"{.github/**/*,*}.{json,json5,yaml,yml,md}": ["prettier --write"], "**/package.json": ["sort-package-json"]}, "prettier": "@abinnovision/prettier-config", "devDependencies": {"@abinnovision/commitlint-config": "^2.2.1", "@abinnovision/prettier-config": "^2.1.3", "@commitlint/cli": "^19.8.0", "@vitest/coverage-v8": "3.0.8", "husky": "^9.1.7", "lint-staged": "^15.4.3", "prettier": "^3.5.3", "sort-package-json": "^3.0.0", "turbo": "^2.5.3", "vitest": "^3.0.8"}, "packageManager": "yarn@4.9.2"}