import * as cheerio from "cheerio";
import { Element, Text } from "domhandler";
import sanitizeHtml from "sanitize-html";

import type { JobCompact, JobDetailed } from "./interface.js";
import type { Cheer<PERSON> } from "cheerio";
import type { Node, AnyNode } from "domhandler";

/**
 * Will parse the given HTML and provide a Cheerio interface for the document.
 */
async function parseDocument(
	html: string | AnyNode,
): Promise<cheerio.CheerioAPI> {
	const options = {
		xml: {
			xmlMode: false,
			recognizeCDATA: true,
			recognizeSelfClosing: true,
			selfClosingTags: true,
			emptyAttrs: true,
		},
	};

	return cheerio.load(html, options, false);
}

/**
 * Will extract a text value in the given element.
 *
 * @param el
 */
function extractTextValue(el: Cheerio<AnyNode>): string | undefined {
	const value = el.first()?.text()?.trim();

	// Treat empty strings as undefined.
	if (value?.length === 0) {
		return undefined;
	}

	return value;
}

/**
 * Will extract the identifier of the "Job" from the given element.
 *
 * @param el
 */
function extractJobId(el: Cheerio<AnyNode>): string | undefined {
	const prefix = "job-id-";

	const classes = el.attr("class");
	if (!classes) {
		return undefined;
	}

	const idClass = classes.split(" ").find((it) => it.startsWith(prefix));
	if (!idClass) {
		return undefined;
	}

	return idClass.substring(prefix.length);
}

/**
 * Will check if the given node contains any text.
 *
 * @param node The node to check against.
 */
function isContainingText(node: Node): boolean {
	if (node instanceof Text) {
		return true;
	}

	if (!(node instanceof Element)) {
		return false;
	}

	return node.children.some((it) => isContainingText(it));
}

/**
 * Will check if the given node contains any text that is wrapped in a bold element.
 *
 * @param node The node to check against.
 */
function isTextWrappedInBold(node: Node): boolean {
	if (!(node instanceof Element)) {
		return false;
	}

	// Exit if the 'b' node has been found.
	if (node.tagName === "b") {
		return true;
	}

	// Check against all children.
	return node.children.some((it) => {
		// If the child does not contain any text, skip it.
		if (!isContainingText(it)) {
			return false;
		}

		// Recursively check the child.
		return isTextWrappedInBold(it);
	});
}

/**
 * Will extract the description of the "Job" from the given element.
 *
 * @param el Element to extract the description from.
 */
function extractJobDescription(el: Cheerio<AnyNode>): string | undefined {
	let filteredElements = el.children();

	// Remove empty paragraphs.
	filteredElements = filteredElements.filter((_, value) => {
		// Remove empty elements.
		if (value.tagName === "p") {
			// Remove empty paragraphs.
			return el.find(value).text().trim() !== "";
		}

		return true;
	});

	// Get the 'ul' elements.
	const listElements = filteredElements.filter((_, value) => {
		return value.tagName === "ul";
	});

	// Skip if there are no 'ul' elements.
	if (listElements.length === 0) {
		return "";
	}

	const elementsToKeep: AnyNode[] = [...listElements];

	const filteredElementsArray = filteredElements.toArray();
	filteredElements.each((selfIndex, selfElement) => {
		if (selfElement.tagName !== "p") {
			return;
		}

		// Find the next 'ul' element.
		const nextUlElement = filteredElementsArray.find((it, idx) => {
			if (idx <= selfIndex) {
				return false;
			}

			return it.tagName === "ul";
		});

		// If there is no next 'ul' element, then we can stop here.
		if (!nextUlElement) {
			return;
		}

		const nextUlElementIndex = filteredElementsArray.indexOf(nextUlElement);
		const nextUlElementDistance = nextUlElementIndex - selfIndex;

		// This is "title" text for the list we're looking for.
		if (nextUlElementDistance === 1) {
			// Only keep the element if it is wrapped in a bold element.
			if (isTextWrappedInBold(selfElement)) {
				elementsToKeep.push(selfElement);
			}
			return;
		}

		if (nextUlElementDistance === 2) {
			// There might be a case
			// where the "title" text also has a subtitle,
			// which we're going to ignore.
			const nextElement = filteredElementsArray[selfIndex + 1];
			if (!isTextWrappedInBold(nextElement)) {
				elementsToKeep.push(selfElement);
			}

			return;
		}
	});

	// Only keep the elements from the list and remove all the others.
	filteredElements = filteredElements.filter((_, element) => {
		return elementsToKeep.includes(element);
	});

	// Sanitize the HTML.
	return sanitizeHtml(filteredElements.toString(), {
		allowedTags: ["span", "p", "ul", "li", "b", "br"],
		enforceHtmlBoundary: true,
		nestingLimit: 20,
		allowedAttributes: {},
		allowedClasses: {},
		allowedIframeDomains: [],
		allowedIframeHostnames: [],
		allowedSchemes: [],
		allowedSchemesAppliedToAttributes: [],
		allowedSchemesByTag: {},
		allowedScriptDomains: [],
		allowedScriptHostnames: [],
		allowedStyles: {},
		allowIframeRelativeUrls: false,
		allowProtocolRelative: false,
		allowVulnerableTags: false,
	});
}

/**
 * Will parse the given HTML and provide all the available Jobs.
 *
 * @param html Raw HTML of the page.
 */
export async function parseJobListDocument(
	html: string,
): Promise<JobCompact[]> {
	const document = await parseDocument(html);

	// Find all elements.
	const tiles = document("li.job-tile");
	if (tiles.length === 0) {
		return [];
	}

	const collector: JobCompact[] = [];

	for (const tileElement of tiles) {
		// Get the tile element with the Cheerio interface.
		const tile = document(tileElement);

		// If the link is not available, skip it.
		const relativeLink = tile.attr("data-url")?.trim();
		if (!relativeLink) {
			continue;
		}

		const jobId = extractJobId(tile);

		const jobLink = `https://jobs.signal-iduna.de${encodeURI(relativeLink)}`;

		const jobTitle = extractTextValue(tile.find(".tiletitle a.jobTitle-link"));

		const jobLocation = extractTextValue(
			tile.find("div.section-field.city div[id$='section-city-value']"),
		);

		const jobDepartment = extractTextValue(
			tile.find("div.section-field.dept div[id$='section-dept-value']"),
		);

		const jobEntryLevel = extractTextValue(
			tile.find(
				"div.section-field.shifttype div[id$='section-shifttype-value']",
			),
		);

		const jobEmploymentType = extractTextValue(
			tile.find("div.section-field.facility div[id$='section-facility-value']"),
		);

		collector.push({
			id: jobId!,
			title: jobTitle!,
			externalLink: jobLink!,
			location: jobLocation!,
			department: jobDepartment!,
			entryLevel: jobEntryLevel!,
			employmentType: jobEmploymentType!,
		});
	}

	return collector;
}

/**
 * Parses the given job from the given HTML.
 *
 * @param job
 * @param html
 */
export async function parseJobDetailsDocument(
	job: JobCompact,
	html: string,
): Promise<JobDetailed> {
	const document = await parseDocument(html);

	// Only available on the V1 page.
	const jobStartDate = extractTextValue(
		document(
			"div.jobDisplayShell div.jobDisplay div.content div.job span[data-careersite-propertyid='productservice']",
		),
	);

	// Only available on the V1 page.
	const jobTimeLimitation = extractTextValue(
		document(
			"div.jobDisplayShell div.jobDisplay div.content div.job span[data-careersite-propertyid='department']",
		),
	);

	const jobDescription = extractJobDescription(
		document(".jobDisplay span.jobdescription"),
	);

	return {
		...job,
		startDate: jobStartDate ?? null,
		timeLimitation: jobTimeLimitation ?? null,
		description: jobDescription!,
	};
}
