import { parseJobDetailsDocument, parseJobListDocument } from "./parser.js";

import type { JobCompact, JobDetailed } from "./interface.js";

const fetchDocument = async (url: string): Promise<string | undefined> => {
	const response = await fetch(url);

	return await response.text();
};

/**
 * Provides all available jobs from the live site. This returns the compact version.
 *
 * @returns All available jobs.
 */
export const fetchAllJobs = async (): Promise<JobCompact[]> => {
	const jobsList = await fetchDocument(
		"https://jobs.signal-iduna.de/SICOREREALASSETS/go/SICORE-Offene-Stellen/9544401/",
	);
	if (!jobsList) {
		return [];
	}

	// Parse the list of jobs and return the compact version.
	return await parseJobListDocument(jobsList);
};

/**
 * Provides the detailed information for the given job.
 *
 * @param job The job to fetch the details for.
 * @returns The detailed information for the job.
 */
export const fetchJobDetailed = async (
	job: JobCompact,
): Promise<JobDetailed> => {
	const jobDetailsDocument = await fetchDocument(job.externalLink);
	if (!jobDetailsDocument) {
		throw new Error("Job could not be resolved");
	}

	return await parseJobDetailsDocument(job, jobDetailsDocument);
};
