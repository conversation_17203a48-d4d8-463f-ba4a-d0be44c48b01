import * as fsp from "fs/promises";
import * as path from "path";
import { beforeAll, describe, expect, it } from "vitest";

import { parseJobDetailsDocument, parseJobListDocument } from "./parser.js";

import type { JobCompact } from "./interface.js";

describe("data/jobs/parser.ts", () => {
	describe("parseJobListDocument", () => {
		let document: string;

		beforeAll(async () => {
			const filePath = path.join(__dirname, "__fixtures__", "list.html");
			document = await fsp.readFile(filePath, "utf-8");
		});

		it("should parse available information from list", async () => {
			const jobs = await parseJobListDocument(document);

			expect(jobs.length).toEqual(6);
		});
	});

	describe("parseJobDetailsDocument", () => {
		// Index 0 on the list.
		let documentV1: string;

		// Index 2 on the list.
		let documentV2: string;

		// Parse the list of jobs as the details page depends on the compact version.
		let jobsCompact: JobCompact[];

		beforeAll(async () => {
			documentV1 = await fsp.readFile(
				path.join(__dirname, "__fixtures__", "details-v1.html"),
				"utf-8",
			);

			documentV2 = await fsp.readFile(
				path.join(__dirname, "__fixtures__", "details-v2.html"),
				"utf-8",
			);

			jobsCompact = await parseJobListDocument(
				await fsp.readFile(
					path.join(__dirname, "__fixtures__", "list.html"),
					"utf-8",
				),
			);
		});

		it("should parse available information from V1 details", async () => {
			const compact = jobsCompact[0];

			const job = await parseJobDetailsDocument(compact, documentV1);

			expect(job).toBeDefined();
			expect(job.title).toEqual("Werkstudent (m/w/d) People & Culture");
			expect(job.location).toBeDefined();
			expect(job.department).toBeDefined();
			expect(job.entryLevel).toBeDefined();
			expect(job.employmentType).toBeDefined();
			expect(job.startDate).toBeDefined();
			expect(job.timeLimitation).toBeDefined();
			expect(job.description).toBeDefined();
			expect(job.description).toContain("Das erwartet dich");
			expect(countOccurrences(job.description, "<ul>")).toEqual(3);
			expect(countOccurrences(job.description, "<p>")).toEqual(3);
		});

		it("should parse available information from V2 details", async () => {
			const compact = jobsCompact[2];

			const job = await parseJobDetailsDocument(compact, documentV2);

			expect(job).toBeDefined();
			expect(job.title).toEqual("Asset Manager (m/w/d) Deutschland");
			expect(job.location).toBeDefined();
			expect(job.department).toBeDefined();
			expect(job.entryLevel).toBeDefined();
			expect(job.employmentType).toBeDefined();
			expect(job.startDate).toBeNull();
			expect(job.timeLimitation).toBeNull();
			expect(job.description).toBeDefined();
			expect(job.description).toContain("Das erwartet Sie");
			expect(countOccurrences(job.description, "<ul>")).toEqual(3);
			expect(countOccurrences(job.description, "<p>")).toEqual(3);
		});
	});
});

/**
 * Counts the number of times a substring occurs in a string.
 *
 * @param input Haystack to search in.
 * @param substring Substring to search for.
 */
function countOccurrences(input: string, substring: string): number {
	return input.split(substring).length - 1;
}
