/**
 * Basic properties of a job.
 */
interface JobBase {
	/**
	 * Unique identifier for the job.
	 */
	id: string;

	/**
	 * Title of the job.
	 */
	title: string;

	/**
	 * Link to the job details page in Success Factors.
	 */
	externalLink: string;
}

/**
 * Properties of the Job, which are available on the list page.
 */
export type JobCompact = JobBase & {
	// Ort
	location: string;

	// Unternehmsbereich
	department: string;

	// Einstiegslevel
	entryLevel: string;

	// Beschäftigungsart
	employmentType: string;
};

/**
 * Properties of the Job, which are available on the details page.
 */
export type JobDetailed = JobCompact & {
	// Eintrittsdatum
	startDate: string | null;

	// Befristung
	timeLimitation: string | null;

	// Description
	description: string;
};
