{"$schema": "https://json.schemastore.org/package.json", "name": "@internal/cache-handler", "version": "0.0.0", "private": true, "license": "proprietary", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc -p tsconfig.build.json", "format:check": "prettier --check '{{src,test}/**/*,*}.{js,jsx,ts,tsx,json,json5,yml,yaml,md}'", "format:fix": "prettier --write '{{src,test}/**/*,*}.{js,jsx,ts,tsx,json,json5,yml,yaml,md}'", "lint:check": "eslint '{{src,test}/**/*,*}.{js,jsx,ts,tsx}'", "lint:fix": "eslint '{{src,test}/**/*,*}.{js,jsx,ts,tsx}' --fix", "test-unit": "vitest --run", "test-unit:watch": "vitest --watch"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{js,jsx,ts,tsx,json,json5,yaml,yml,md}": ["prettier --write"]}, "browserslist": "defaults, not ie <= 11", "prettier": "@abinnovision/prettier-config", "devDependencies": {"@abinnovision/eslint-config-base": "^2.2.0", "@abinnovision/eslint-config-react": "^2.2.0", "@abinnovision/eslint-config-typescript": "^2.2.1", "@abinnovision/prettier-config": "^2.1.3", "eslint": "^9.22.0", "globals": "^16.0.0", "prettier": "^3.5.3", "typescript": "^5.8.2", "vitest": "^3.0.8"}, "dependencies": {"@neshca/cache-handler": "^1.9.0", "iovalkey": "^0.3.1"}}