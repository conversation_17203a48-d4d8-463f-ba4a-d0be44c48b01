import { cache<PERSON><PERSON><PERSON>Logger } from "./utils.js";

import type { <PERSON>ache<PERSON><PERSON>lerValue, <PERSON><PERSON> } from "@neshca/cache-handler";
import type { Redis } from "iovalkey";

interface CacheHandlerOpts {
	client: Redis;
	keyPrefix?: string;
}

const _logger = cacheHandlerLogger.child({ module: "iovalkey-handler" });

const _KIND_ROUTE = "APP_ROUTE";

/**
 * Checks if the given tag is an implicit tag.
 * This is intended to be copied from the cache-handler package
 * because otherwise we're importing packages we don't need.
 *
 * @param tag The tag to check.
 */
function isImplicitTag(tag: string) {
	return tag.startsWith("_N_T_");
}

export const createIOValkeyHandler = (opts: CacheHandlerOpts): Handler => {
	const { client, keyPrefix } = opts;

	// Common constants.
	const sharedTagsKey = "_sharedTags_";
	const revalidatedTagsKey = `${keyPrefix}__revalidated_tags__`;

	_logger.debug({ opts }, "Creating IOValkeyHandler");

	return {
		name: "io-valkey-strings",

		// We do not use try/catch blocks in the Handler methods.
		// CacheHandler will handle errors and use the next available Handler.
		async get(key, meta) {
			_logger.debug({ key, meta }, "Calling #get");

			const { implicitTags } = meta;

			// Get the value from Valkey.
			// We use the key prefix to avoid key collisions with other data in Valkey.
			const result = await client.get(keyPrefix + key);

			// If the key does not exist, return null.
			if (!result) {
				return null;
			}

			// Valkey stores strings, so we need to parse the JSON.
			const cacheValue: CacheHandlerValue = JSON.parse(result);

			// The body is base64 encoded, so we need to decode it.
			if (cacheValue.value?.kind === _KIND_ROUTE) {
				cacheValue.value.body = Buffer.from(
					cacheValue.value.body as unknown as string,
					"base64",
				);
			}

			// If the cache value has no tags, return it early.
			if (!cacheValue) {
				return null;
			}

			// Get the set of explicit and implicit tags.
			// implicitTags are available only on the `get` method.
			const combinedTags = [...cacheValue.tags, ...implicitTags];

			// If there are no tags, return the cache value early.
			if (combinedTags.length === 0) {
				return cacheValue;
			}

			// Get the revalidation times for the tags.
			const revalidationTimes = await client.hmget(
				revalidatedTagsKey,
				...combinedTags,
			);

			// Iterate over all revalidation times.
			for (const timeString of revalidationTimes) {
				// If the revalidation time is greater than the last modified time of the cache value,
				if (
					timeString &&
					Number.parseInt(timeString, 10) > cacheValue.lastModified
				) {
					// Delete the key from Valkey.
					await client.unlink(keyPrefix + key);

					// Return null to indicate cache miss.
					return null;
				}
			}

			// Return the cache value.
			return cacheValue;
		},
		async set(key, cacheHandlerValue) {
			// Explicitly only log the key because the cacheHandlerValue can be very large.
			_logger.debug({ key }, "Calling #set");

			let _enhancedValue: any = cacheHandlerValue.value;

			if (_enhancedValue && _enhancedValue.kind === _KIND_ROUTE) {
				_enhancedValue = {
					..._enhancedValue,
					// Encode the body as base64 to avoid issues with the JSON.stringify() method.
					body: _enhancedValue.body.toString("base64"),
				};
			}

			const setOperation = client.set(
				keyPrefix + key,
				JSON.stringify({
					...cacheHandlerValue,
					value: _enhancedValue,
				}),
			);

			// If the cacheHandlerValue has a lifespan, set the automatic expiration.
			// cacheHandlerValue.lifespan can be null if the value is the page from the Pages Router without getStaticPaths or with `fallback: false`
			// so, we need to check if it exists before using it
			const expireOperation = cacheHandlerValue.lifespan
				? client.expireat(keyPrefix + key, cacheHandlerValue.lifespan.expireAt)
				: undefined;

			// If the cache handler value has tags, set the tags.
			// We store them separately to save time to retrieve them in the `revalidateTag` method.
			const setTagsOperation = cacheHandlerValue.tags.length
				? client.hset(
						keyPrefix + sharedTagsKey,
						key,
						JSON.stringify(cacheHandlerValue.tags),
					)
				: undefined;

			await Promise.all([setOperation, expireOperation, setTagsOperation]);
		},
		async revalidateTag(tag) {
			_logger.debug({ tag }, "Calling #revalidateTag");

			// Check if the tag is implicit.
			// Implicit tags are not stored in the cached values.
			if (isImplicitTag(tag)) {
				// Mark the tag as revalidated at the current time.
				await client.hset(revalidatedTagsKey, tag, Date.now());
			}

			// Create a map to store the tags for each key.
			const tagsMap = new Map<string, string[]>();

			// Cursor for the hScan operation.
			let cursor = "0";

			// Iterate over all keys in the shared tags.
			do {
				const page: Awaited<ReturnType<typeof client.hscan>> =
					await client.hscan(keyPrefix + sharedTagsKey, cursor, "COUNT", 100);

				const rawValues = page[1];

				// The layout of the hscan response is as follows:
				// [cursor, key1, value1, key2, value2, ...]
				for (let i = 0; i < rawValues.length; i += 2) {
					const key = rawValues[i];
					const value = rawValues[i + 1];
					tagsMap.set(key, JSON.parse(value));
				}

				cursor = page[0];
			} while (cursor && cursor !== "0");

			// Create an array of keys to delete.
			const keysToDelete: string[] = [];

			// Create an array of tags to delete from the hash map.
			const tagsToDelete: string[] = [];

			// Iterate over all keys and tags.
			for (const [key, tags] of tagsMap) {
				// If the tags include the specified tag, add the key to the delete list.
				if (tags.includes(tag)) {
					// Key must be prefixed because we use the key prefix in the set method.
					keysToDelete.push(keyPrefix + key);
					// Set an empty string as the value for the revalidated tag.
					tagsToDelete.push(key);
				}
			}

			// If there are no keys to delete, return early.
			if (keysToDelete.length === 0) {
				return;
			}

			// Delete the keys from Valkey.
			const deleteKeysOperation = client.unlink(keysToDelete);

			// Update the tags in Valkey by deleting the revalidated tags.
			const updateTagsOperation = client.hdel(
				keyPrefix + sharedTagsKey,
				...tagsToDelete,
			);

			// Wait for all operations to complete.
			await Promise.all([deleteKeysOperation, updateTagsOperation]);
		},
	};
};
