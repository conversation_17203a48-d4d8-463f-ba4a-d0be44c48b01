import { Redis } from "iovalkey";
import { parseURL } from "iovalkey/built/utils/index.js";

import { cacheHandlerLogger } from "./utils.js";

import type { RedisOptions } from "iovalkey";

const _logger = cacheHandlerLogger.child({ module: "iovalkey-factory" });

/**
 * Parses the sentinel string into an array of sentinel objects.
 *
 * @param input The input string.
 */
const parseSentinelsString = (
	input: string,
): { host: string; port: number }[] => {
	const separated = input.split(",");

	const sentinels = [];
	for (const item of separated) {
		if (!item.includes(":")) {
			continue;
		}

		const [host, port] = item.split(":");
		if (!host || !port) {
			continue;
		}

		sentinels.push({ host, port: Number(port) });
	}

	return sentinels;
};

/**
 * Creates a Redis instance from the environment variables.
 *
 * @returns Created Redis instance.
 */
export const createIOValkeyFromEnv = async (): Promise<Redis> => {
	let options: RedisOptions = {
		enableReadyCheck: true,

		// Don't wait indefinitely for the connection to be established.
		maxRetriesPerRequest: 5,

		// Set sufficient defaults for the timeouts.
		connectTimeout: 1000,
		commandTimeout: 5000,

		// We're going to connect lazily to valkey to await the connection.
		// This is because we don't want to block until the connection is established.
		lazyConnect: true,
	};

	if (process.env.VALKEY_URL) {
		options = {
			...parseURL(process.env.VALKEY_URL),
			...options,
		};
	} else if (process.env.VALKEY_SENTINELS) {
		options.sentinels = parseSentinelsString(process.env.VALKEY_SENTINELS);
		options.name = process.env.VALKEY_SENTINEL_NAME ?? "mymaster";
	}

	// For debugging purposes, log the options.
	_logger.debug({ options }, "Creating Valkey instance");

	const redis = new Redis(options);

	// Listen for errors and log them.
	redis.on("error", (err) => {
		_logger.warn({ err }, "Valkey error");
	});

	// Wait for the connection to be established.
	await redis.connect();

	return redis;
};
