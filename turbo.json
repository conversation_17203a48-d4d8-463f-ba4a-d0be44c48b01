{"$schema": "https://turbo.build/schema.json", "tasks": {"start": {"dependsOn": ["build"], "persistent": true, "cache": false}, "start:dev": {"dependsOn": ["generate"], "persistent": true, "cache": false}, "fix": {"dependsOn": ["format:fix", "lint:fix"]}, "check": {"dependsOn": ["format:check", "lint:check"]}, "format:fix": {}, "format:check": {}, "lint:fix": {}, "lint:check": {}, "build": {"inputs": ["src/**/*"], "outputs": ["dist/**/*", ".next/**", "!.next/cache/**"], "dependsOn": ["generate", "^build"], "env": ["BUILD_VERSION", "BUILD_COMMIT", "SENTRY_AUTH_TOKEN"]}, "generate": {"inputs": ["src/**/*"], "outputs": ["generated/**/*"], "dependsOn": ["^build"]}, "test-unit": {"dependsOn": ["generate", "^build"], "inputs": ["src/**/*", "test/**/*"]}, "test-integration": {"dependsOn": ["generate", "^build"], "inputs": ["src/**/*", "test/**/*"]}, "test-e2e": {"dependsOn": ["generate", "^build"], "inputs": ["src/**/*", "test/**/*"]}}}