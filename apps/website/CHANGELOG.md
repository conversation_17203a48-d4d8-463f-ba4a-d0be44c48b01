# Changelog

## [1.9.14](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.13...v1.9.14) (2025-05-31)


### Bug Fixes

* check window variable before accessing ([#208](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/208)) ([011eacb](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/011eacb92a6569e088f19ae7061a20848a22e1ed))
* upgrade nextjs ([#192](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/192)) ([6fd37f1](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/6fd37f127998034a88c3a20a7ddcf95afc7dcfec))

## [1.9.13](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.12...v1.9.13) (2025-05-27)


### Bug Fixes

* use class names only on children ([#190](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/190)) ([4065714](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/40657147fe657cdf9b12ef26d940a922af6c8a0a))

## [1.9.12](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.11...v1.9.12) (2025-05-27)


### Bug Fixes

* add option to include collections in active state of header item ([#187](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/187)) ([030e940](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/030e940f228cb3ef50d5dc58fb502b2d03f0c967))
* use static gaps within section ([#189](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/189)) ([aca7bfa](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/aca7bfa940850cf3b280e0bb9d270d7b7d4dcca1))

## [1.9.11](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.10...v1.9.11) (2025-05-26)


### Bug Fixes

* add null check ([#185](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/185)) ([5fdd26f](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/5fdd26f1d9d3927cdfa9e31d5b688e6d6e39c8aa))

## [1.9.10](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.9...v1.9.10) (2025-05-25)


### Bug Fixes

* add persistent card slider ([#181](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/181)) ([d7a8dae](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/d7a8daee7d4a5ca22c3de37f2ace63d4ca5fdcec))
* add validate function to project details facts module ([#179](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/179)) ([c8e2a7a](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/c8e2a7ab81ffbe1d48a3df34132f9f58ef0ae903))
* enable folders in media collection ([#184](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/184)) ([2f07def](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/2f07def54ec8f6cedf8751647e2dd43414c6b3ce))
* handle undefined window ([#182](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/182)) ([b1747bb](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/b1747bbeffb7530e6caf3421e39bc5a29ee2f69b))
* upgrade payloadcms ([#183](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/183)) ([e0ecbc8](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/e0ecbc89042b173584718702a98607da9fc5564e))

## [1.9.9](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.8...v1.9.9) (2025-05-25)


### Bug Fixes

* add a validate function to check if a block can render ([#177](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/177)) ([bb53e01](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/bb53e0156f7ea4b0f7486b90a0eddb2b6e2a51dd))
* add project details description ([#178](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/178)) ([5f144a7](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/5f144a7fc898f8c28ba463236fb13f82864c8328))
* pause animation if document is not visible ([#175](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/175)) ([3291425](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/329142545afbf60dbd4f17af0b41549d517ff3bd))

## [1.9.8](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.7...v1.9.8) (2025-05-24)


### Bug Fixes

* stabilize person grid ([#173](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/173)) ([feb49f6](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/feb49f693a8c01e6143853a3ab68bf47476d21db))

## [1.9.7](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.6...v1.9.7) (2025-05-23)


### Bug Fixes

* add ability to define meta attributes for document shells ([#172](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/172)) ([450a7fb](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/450a7fb598c36ec68bc75f0dc808f924bdb18a6f))
* add ability to use multiple fields for slug generation ([#170](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/170)) ([c824929](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/c824929e36666437bd321aa945a93b639812ca44))
* unify document shell rendering ([#169](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/169)) ([5ab5128](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/5ab512869c846dfae00425101abaf18e193e4e72))

## [1.9.6](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.5...v1.9.6) (2025-05-23)


### Bug Fixes

* add ability to exclude persons from display ([#167](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/167)) ([3d07f08](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/3d07f08b1152003cb97c927fe02f36bf835a6cbb))
* add facts list component ([#164](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/164)) ([008413d](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/008413d626197e35b3d3665a56633ecaff3fb785))
* highlight active menu item ([#166](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/166)) ([dfc6a97](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/dfc6a972e608e88a82375a14ae9f0136c629c8f9))
* use css only for payload custom styles ([#168](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/168)) ([fcc2ba8](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/fcc2ba85bc2d1dc140a5acd30525939fa11caa13))

## [1.9.5](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.4...v1.9.5) (2025-05-22)


### Bug Fixes

* add project description field ([#162](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/162)) ([970e45b](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/970e45b0553ede60747dbc30e183de9f2e84e013))

## [1.9.4](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.3...v1.9.4) (2025-05-21)


### Bug Fixes

* add position relative to intro module ([#161](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/161)) ([df892bd](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/df892bddd6d4dd330197eab1038a3095d9c77089))
* normalize phone numbers in contact banner module ([#159](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/159)) ([47db940](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/47db9407774fac9607fc3c844e6a79d919fadb2a))

## [1.9.3](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.2...v1.9.3) (2025-05-21)


### Bug Fixes

* apply section id from block configuration ([#155](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/155)) ([e4efd24](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/e4efd24c5ab1194c0eb25e9965eb8f18c7f3ee57))
* hide project object address fact if not available ([#157](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/157)) ([e63a1bc](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/e63a1bcb4efd13ccaccabcf79d5cd2e7a1fb98b1))
* redirect /en to /de temporary ([#158](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/158)) ([7d679f0](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/7d679f0e18e17ae9ece609e6fbb8a235ee683488))
* sort by relationship input ([#154](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/154)) ([7a5b29e](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/7a5b29e9e40ac0c419546e4425c6a0c28f3c790b))

## [1.9.2](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.1...v1.9.2) (2025-05-20)


### Bug Fixes

* set default limit in person grid module ([#152](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/152)) ([f74a47d](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/f74a47dfd91c21e5c687763f52f40980705d3459))

## [1.9.1](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.9.0...v1.9.1) (2025-05-20)


### Bug Fixes

* add id attribute to section wrapper module ([#149](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/149)) ([8f9caae](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/8f9caae180ce034488a3cae41c7a8643b0391db9))
* add sorting in jobs table module ([#150](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/150)) ([c22aec5](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/c22aec5d42619c7edc11914507f12a971dd04a79))

## [1.9.0](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.8.6...v1.9.0) (2025-05-20)


### Features

* add jobs list and document ([#148](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/148)) ([f4d36c5](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/f4d36c522fd4fa404e4c9436f7bffdd3c9b46901))


### Bug Fixes

* refactor person grid module ([#146](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/146)) ([b360e64](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/b360e64e461065b7e769d5ab297ba351368be0d8))

## [1.8.6](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.8.5...v1.8.6) (2025-05-20)


### Bug Fixes

* use square aspect ratio for person images ([#143](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/143)) ([680cc78](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/680cc78689cff88941473fd232f189533e938922))
* use static image as module gradient background ([#145](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/145)) ([ef29d1c](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/ef29d1ce2d16f3fee64ac9fed950d3e34bfc16b9))

## [1.8.5](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.8.4...v1.8.5) (2025-05-14)


### Bug Fixes

* always add actions container on presentation items module ([#141](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/141)) ([61c4281](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/61c42813eb4caf8bc19a82960aa478b9e5e49a2d))

## [1.8.4](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.8.3...v1.8.4) (2025-05-13)


### Bug Fixes

* align highlight gradient ([#138](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/138)) ([1131d5b](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/1131d5b2f2ad62df21f77785a99573d12a8eeede))
* enforce person grid constraints ([#139](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/139)) ([287b0f9](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/287b0f9742ebe6a55c3161c40f303e4bba6814d2))

## [1.8.3](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.8.2...v1.8.3) (2025-05-13)


### Bug Fixes

* adjust person grid replacement with placeholder ([#136](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/136)) ([a66e41f](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/a66e41f8f7268a6dc92dbb925ffb7e5c2f76480b))

## [1.8.2](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.8.1...v1.8.2) (2025-05-13)


### Bug Fixes

* sort news by publishDate ([#134](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/134)) ([a519d29](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/a519d29f2e3f09cf1f9009f68c744c789fb7fe6a))

## [1.8.1](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.8.0...v1.8.1) (2025-05-13)


### Bug Fixes

* align presentation items module content ([#131](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/131)) ([2475679](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/2475679d7b7b4de7eac845ee7b152960dda6b6bc))
* format super- and subscript with corresponding tags ([#132](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/132)) ([d991189](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/d991189f13f6dc5be46934f1f180dff841d0d199))

## [1.8.0](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.7.1...v1.8.0) (2025-05-13)


### Features

* add news list/details ([#129](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/129)) ([ecd1b4e](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/ecd1b4ea1bc0ff6b54c0b668c812216baa53c471))

## [1.7.1](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.7.0...v1.7.1) (2025-05-06)


### Bug Fixes

* add auto hyphens break ([#127](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/127)) ([4c43760](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/4c4376039cadca0dda0bc453de01ff7d9d4b0b46))

## [1.7.0](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.6.5...v1.7.0) (2025-05-06)


### Features

* implement value grid module ([#123](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/123)) ([b9d21da](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/b9d21da9c922e95e08e6134c1ce4a86291512310))


### Bug Fixes

* add the ability to link to projects ([#126](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/126)) ([426cbca](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/426cbcab73c81b2904c22876e5485844938ef95c))

## [1.6.5](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.6.4...v1.6.5) (2025-05-06)


### Bug Fixes

* upgrade sharp dependency ([#122](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/122)) ([356713f](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/356713f218520bfb06e5b9620aee01f59301f8ec))

## [1.6.4](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.6.3...v1.6.4) (2025-05-05)


### Bug Fixes

* properly resolve path when in preview ([#120](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/120)) ([eddb377](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/eddb3770ccd88a7060298086c5616bab47d65f5e))
* use context path as default fallback ([#119](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/119)) ([9820287](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/982028704ab67049f6d3780c162800df02fcf555))

## [1.6.3](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.6.2...v1.6.3) (2025-05-04)


### Bug Fixes

* align number grid module min-max boundaries ([#117](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/117)) ([aa8eedc](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/aa8eedcbc4cecefaf74dc2da2046cb0c12169a88))

## [1.6.2](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.6.1...v1.6.2) (2025-05-04)


### Bug Fixes

* add limit to page lookup fetch ([#115](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/115)) ([640e13b](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/640e13b39ab8101941f120e625c4126b57ec223a))

## [1.6.1](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.6.0...v1.6.1) (2025-05-04)


### Bug Fixes

* align component mappings with caching ([#111](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/111)) ([7e82fd9](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/7e82fd9c066e4b5ad40109d6e3ed6bd23f60779a))
* set facts as not required ([#113](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/113)) ([a107ac9](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/a107ac9f52575d193624701b4d48e09a13f817c2))
* temporarily disable English as a language ([#114](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/114)) ([e947dce](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/e947dcea54889fd9eaf25590f4829b26fee003a0))

## [1.6.0](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.5.4...v1.6.0) (2025-05-04)


### Features

* implement dynamic collection mapping ([#110](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/110)) ([016b33a](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/016b33a7254f4c657748df17b7f9a657653bb667))


### Bug Fixes

* align intro actions to bottom ([#109](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/109)) ([333bdfa](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/333bdfa527a6402cb1954e8e4b908773fc32d0c0))
* align project model with city ([#107](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/107)) ([1377924](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/13779247fba42f6e732e3eaced6a10a5a4d36704))

## [1.5.4](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.5.3...v1.5.4) (2025-04-29)


### Bug Fixes

* add adjustable hero image size ([#103](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/103)) ([7fea021](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/7fea021478ca11fddafb84c491675fdd4c69cac1))

## [1.5.3](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.5.2...v1.5.3) (2025-04-29)


### Bug Fixes

* add alignment to section header module ([#100](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/100)) ([a90215c](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/a90215c7f5c0a5b2cd225990037c2d25889ed29a))
* add href to download button ([#101](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/101)) ([e8d04ba](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/e8d04bab6dcb1c0586f74b9738b38207ed075470))

## [1.5.2](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.5.1...v1.5.2) (2025-04-29)


### Bug Fixes

* download button support ([#99](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/99)) ([25f7d89](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/25f7d892800a145ca33d53cf7d8f7d38dfdfb629))
* properly set the blurhash property ([#97](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/97)) ([c68be6a](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/c68be6a37af1f404f0fa6b857488d8ccae0c47f5))

## [1.5.1](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.5.0...v1.5.1) (2025-04-29)


### Bug Fixes

* wrap plaiceholder generation in try-catch ([#95](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/95)) ([1e40996](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/1e40996c9da59a8a7ae175c1d5b8a6d5b9d79cc5))

## [1.5.0](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.4.1...v1.5.0) (2025-04-29)


### Features

* dynamic graphics module ([#94](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/94)) ([00ef43c](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/00ef43cc47d7a9aa74ffc8aeca70ac583180de7e))


### Bug Fixes

* add links to highlight grid ([#93](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/93)) ([a831908](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/a831908ad3cf4d2c96c2396edd5bb53e54a03d82))
* add per-row configuration option to numbers grid ([#85](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/85)) ([ca27fa6](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/ca27fa68bccbcd06273a6c9ac409ed555117e7aa))
* align s3 storage adapter configuration ([#88](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/88)) ([b034ad5](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/b034ad5f12fa950aee50ddd0b7b2d7423d2e667e))
* upgrade payloadcms ([#87](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/87)) ([4316c3d](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/4316c3dba479e2a0d1cda375691678fa39ed1a47))

## [1.4.1](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.4.0...v1.4.1) (2025-04-15)


### Bug Fixes

* add adjustable page size ([#82](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/82)) ([30a9eb7](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/30a9eb7b709b568b2a73c7675f6bf89b95facc03))
* extend media collection ([#84](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/84)) ([27974de](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/27974de581025e209a681b9526d2222a8393e899))

## [1.4.0](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.3.0...v1.4.0) (2025-04-15)


### Features

* persons grid ([#80](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/80)) ([8645677](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/8645677dcae74b8dde29f40ef4dd13d9cd36bc22))


### Bug Fixes

* add actions to section header module ([#81](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/81)) ([d9a8ce5](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/d9a8ce554d8e50f117d0daff8c8bee1a214c079e))
* support preview mode ([#78](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/78)) ([d3734fb](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/d3734fb9bcc049215bd31d581f92927535af606b))

## [1.3.0](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.2.1...v1.3.0) (2025-04-15)


### Features

* add data resolvers and implement projects list module ([#73](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/73)) ([8741a26](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/8741a265476ccdbdfd2664d1aaefadda237c60c0))


### Bug Fixes

* add pagination ([#75](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/75)) ([e515235](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/e515235cab201ed10968dfcfe533d0794bf3ab64))
* enable versioning on global blocks ([#77](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/77)) ([8710f23](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/8710f236f7271f7462a840a5449498f6f7193a6e))
* preview support in links ([#76](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/76)) ([01c5685](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/01c56852a3ebb5862a5fe494a5c744530117087d))

## [1.2.1](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.2.0...v1.2.1) (2025-04-13)


### Bug Fixes

* add overflow-hidden to module wrapper ([8296bac](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/8296bacd060ee5e90336959c15969e77ac6d54f6))
* remove double padding on hidden divider ([#71](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/71)) ([dfd0cb0](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/dfd0cb0d8e5972974d8bf3a5630d8bcade5a9375))
* title-icon combination ([9add6a8](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/9add6a8448303b945288e5c1ef636eed3be58a70))

## [1.2.0](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.1.11...v1.2.0) (2025-04-13)


### Features

* implement world map module ([#65](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/65)) ([1b4095a](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/1b4095a161d6fade48e93bb0e9fa8ddd0997b3ce))


### Bug Fixes

* align composable presentation module gradient ([#67](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/67)) ([f554d7a](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/f554d7ab3c14da4df6ba4cb10c92dce749f854ba))
* align presentation items module ([#68](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/68)) ([1e83b9f](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/1e83b9f983edccf86be840e0978dbf64c88cd9f9))
* generic slug field ([#69](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/69)) ([69d1212](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/69d12126d7fdc6e720d8d6798d229ab07a40b12c))
* module layout adjustments ([#70](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/70)) ([9a70d76](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/9a70d762cd2d65c910df79005cb42da1ee1fe6d2))

## [1.1.11](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.1.10...v1.1.11) (2025-04-08)


### Bug Fixes

* remove image background for loading ([#64](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/64)) ([c33dd9e](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/c33dd9e335257182cea82b58b7b129c0db80dccb))
* title uppercase switch ([#62](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/62)) ([149ed4b](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/149ed4b85be8c8fdd4f71efd9191257316f1791a))

## [1.1.10](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.1.9...v1.1.10) (2025-04-08)


### Bug Fixes

* add toggle to show/hide divider on section ([#59](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/59)) ([e5e77e3](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/e5e77e3223b5e5a3f1131abdb01f75357afb2160))
* align prose configuration ([#61](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/61)) ([e9b04bb](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/e9b04bb7d7e592ffe9e65c0b3f65043018b78394))

## [1.1.9](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.1.8...v1.1.9) (2025-04-02)


### Bug Fixes

* add rbac ([#57](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/57)) ([cda2967](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/cda2967eec762a72da454abb56a2af9afb952447))

## [1.1.8](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.1.7...v1.1.8) (2025-04-01)


### Bug Fixes

* add image icon to composable presentation module ([#55](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/55)) ([1702d19](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/1702d198b489424463d40ba8cc597c2dc5864de7))

## [1.1.7](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.1.6...v1.1.7) (2025-04-01)


### Bug Fixes

* add icon to presentation items module ([#53](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/53)) ([d6bf81e](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/d6bf81e6899e9e887bf8f6c09d20d3a3550871a6))

## [1.1.6](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.1.5...v1.1.6) (2025-03-31)


### Bug Fixes

* add download module ([#52](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/52)) ([9445e8f](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/9445e8f6188fa273af499318a995863cbe864a0e))
* align project data model ([#49](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/49)) ([e143f24](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/e143f242c23c8d01ec80b7f120493cf0cd3f59f2))
* use grid instead of flex for composable presentation module ([#51](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/51)) ([fa3878b](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/fa3878b8848f4778c5a216a6c9ffdd3c0fb44e72))

## [1.1.5](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.1.4...v1.1.5) (2025-03-26)


### Bug Fixes

* align page published query ([#47](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/47)) ([40ce37d](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/40ce37dc9e72b1235904169c1e080796f3dcca89))

## [1.1.4](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.1.3...v1.1.4) (2025-03-25)


### Bug Fixes

* only include published documents for page index on non-draft ([#45](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/45)) ([e72a515](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/e72a5157b0e2e2b755d623afafe8b23aa6624a95))

## [1.1.3](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.1.2...v1.1.3) (2025-03-25)


### Bug Fixes

* include draft in preview mode for payload lookup ([#41](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/41)) ([682eb7c](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/682eb7c6440133e17ee5b4b1f3b5505297cb8bc3))

## [1.1.2](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.1.1...v1.1.2) (2025-03-24)


### Bug Fixes

* implement new modules ([#38](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/38)) ([519de0e](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/519de0e2e755cbeca665793d30ea376af1082b50))
* use separate line for each element in contact banner ([#40](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/40)) ([14c18c7](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/14c18c7fa38d15b89d27ea8c92ab695f3105e518))

## [1.1.1](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.1.0...v1.1.1) (2025-03-23)


### Bug Fixes

* align hero module for long words ([#37](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/37)) ([140af78](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/140af78857e67897569124c0ba9f084ae23abadb))
* dynamic highlighted color ([#34](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/34)) ([dead4a5](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/dead4a50cef06415178735b9a321871b79ada5d6))
* remove typo character ([#35](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/35)) ([5d52d7d](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/5d52d7d636620bb183abd1a11f97e868fb9a914a))

## [1.1.0](https://github.com/hansainvest-real/SICORE_Home_Mono/compare/v1.0.0...v1.1.0) (2025-03-21)


### Features

* live preview support ([#31](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/31)) ([f4c3776](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/f4c37766b6159982b8387ca82f85d9e902e2ff2b))

## 1.0.0 (2025-03-18)


### Features

* add cookiebot and google analytics ([#24](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/24)) ([e1df34c](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/e1df34c0defd27e4eec9389d106586d3f5fd071b))
* add payload ([8547dde](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/8547dde9ff46b18a440c31f696c8d904e4e2ef16))
* cache handler based on valkey ([2ac0796](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/2ac0796cd2b78f52203239ad5b4ddb5254471784))
* init ([09c085b](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/09c085bc13f27ac688f8246a13c21c3267d789ae))
* jobs collection with fetch ([d503900](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/d50390073c11cc7aced95c58227d59f0da7b5bca))
* mobile menu ([#8](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/8)) ([efed900](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/efed9001c6e253ccb1501a38efd0da6a4e513723))
* update ([f790b93](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/f790b937add0acce8e38aa1410ad29b4533f2008))
* update ([977b053](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/977b053719adf7cf51000bcc1efe7f1689387e3a))
* update ([b734c6c](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/b734c6c289b347e1ecf296006e7d0f789f41530f))
* update ([280d528](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/280d528f985fe095665fa87e688f43dea9a3f8e8))
* update ([27e674a](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/27e674a4c5b76bf35c2ea3479b274292382ed927))
* update ([4ad0ae0](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/4ad0ae0eff7da5ae419863a1a88dae9ea9a70675))
* update ([60941de](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/60941de2dfeed9c7d96793e73c796a3288d1439e))
* update ([05839ed](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/05839ed4673573853872b30356b26f67def594d9))


### Bug Fixes

* ability to disable search in header ([53dc714](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/53dc714da256d4e13e45b572a86b568b241c10cd))
* add alternate languages meta ([#17](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/17)) ([67e1601](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/67e1601f6e3f80c21fdcd6fbaa7cf96c2c878072))
* add animation to hero module ([b60eaf3](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/b60eaf3d2438008f6da1a97aae7f61a748e84274))
* add build version to comment ([#29](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/29)) ([fb6f899](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/fb6f899e70c056f245a8ea7bfb27244267a8d455))
* add cache tags and revalidate on ([58ad269](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/58ad2692e1f0bf5c75491053049d2f350a62676f))
* add health endpoint ([ba002bb](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/ba002bbb87ff40d584b3bfa5167bd730257d47a0))
* add modules ([674bc51](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/674bc51008be2361bdbb83f56ee698a3ad81d207))
* add presentation items module ([#25](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/25)) ([2bb8dd1](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/2bb8dd177e91b833a20f7ba714d0f72192b4a80d))
* add row label to highlight grid module ([3fd1302](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/3fd1302e7e0a2997d8418f2ef0723bfc285289a6))
* add sicore branding ([#5](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/5)) ([84df55d](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/84df55d513101bff2107251c0431d3945559db47))
* adjust image sizes of ProjectsSliderModule ([#13](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/13)) ([223025a](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/223025a274a175d01ea9cbc73dded346c37d49c4))
* adjust locale mapping ([bb076f8](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/bb076f8da2b9aa8a46aadc4e11614bb44da48d1c))
* align animations ([#15](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/15)) ([e23da25](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/e23da25935268323c28726b45956a5a45c7204da))
* align font weight ([#20](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/20)) ([6b03c77](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/6b03c771dd4c8a1930be74fd25b9b96bcf336330))
* align header links ([ec7c78b](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/ec7c78bd62e9f31212ea33f127d295bff3a81b12))
* align image sizes ([#11](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/11)) ([4a136de](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/4a136de41cdd3946ee3234ebc14381a5dfb87104))
* align intro module alignment ([#23](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/23)) ([62d4898](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/62d4898639a68622079cf5f380faf1a61e52fdcd))
* align link field ([865a925](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/865a92527db28977251b85ed319eb1e6ab1fd42b))
* align numbers grid module layout ([#21](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/21)) ([2f03d14](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/2f03d146e243c483a5d506323bef6ed3bc95659c))
* align numbers grid text size ([#10](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/10)) ([ff1c456](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/ff1c456de927d3452a81f69f8af6d595264b8933))
* align numbers grid text sizing ([9b74cc0](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/9b74cc05255959ad2dc519a37abe4ade1755e82e))
* align responsiveness ([151205c](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/151205cbc93ae9a8c970478262a2280da91c9aaf))
* align section viewport margin ([#14](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/14)) ([655675f](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/655675fdb6343932a32c1c29d6d81df7b1300d44))
* align text sizes ([2197ad6](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/2197ad692e417f710d25d8c1ff4a38052c34d0f7))
* align text sizings ([5148807](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/5148807ff8d0b6fdef706b8944319dd10b5189b0))
* button action ([a46c335](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/a46c335ac4e5ef0fc78a4753463bdee97f4e0e4a))
* close the mobile menu on link click ([#19](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/19)) ([868f237](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/868f237e9aece52d4f57f04304cf7dfb4dc68bbd))
* contact banner layout alignment ([#22](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/22)) ([0778c46](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/0778c4690df7c84330d1cb9d8b44c551462fbe1c))
* contact banner module ([2dd31d3](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/2dd31d3ae4956ec0349e7ea033e88607ff4bd965))
* disable auto index on mongodb ([8936101](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/8936101d28c79dd90be6bf8a19f3ca03896dadfd))
* enable english locale ([c1530b5](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/c1530b51729f95b79088e16862f36bed77aa7eaa))
* enable scroll spy for count up component ([b7438de](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/b7438dea0cfa38927454bd427f4a4ac887d8a069))
* enhance animations on modules ([#12](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/12)) ([a0dcfe1](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/a0dcfe1ff24a3625e8d139339cbf00a10536c37c))
* first payload draft ([204d4db](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/204d4db17a55ea9365b0f757608cf534cd465a61))
* hide section separator on last section ([636bbc4](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/636bbc4d9ff5aff2b1d97ea9505adc5d14ab5d15))
* link resolving ([3de7e75](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/3de7e75f0c5e66affe18cfc5a8f5fcecfd600765))
* localized footer ([#6](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/6)) ([6b6d58a](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/6b6d58a469c15892c05c8900a361ad1a6b18e0c0))
* localized sicore migration modal ([#7](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/7)) ([fa5b6e4](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/fa5b6e437a9c3b7f5103faf552a380d7a8854eb1))
* managed modal ([cd7c6dd](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/cd7c6dd9c02e42364e87fb2b574b6b6cfe918411))
* migrate health endpoint to /api/health ([b0c5dbc](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/b0c5dbca7fe95b99ae5578f17ae1b0be7ce12587))
* module wrapper ([8b858b8](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/8b858b8fa034e0b4fd87187d0817e05f94af7256))
* presentation module adjustment ([951b506](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/951b506509d69fff934d044597f61b3d2c2f2d4a))
* properly set defaults in next config ([caec210](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/caec210c4ce78854118dadca1991cf9c70f04aa1))
* refactor cache tag handling ([#18](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/18)) ([daa52c4](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/daa52c42394efe8e29e05ac32f589aefd2efc20b))
* refactor links and implement actions ([#28](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/28)) ([7dde849](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/7dde849fda21a66128158e835ab598f76be88da9))
* refactor payload collections ([#2](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/2)) ([4804a29](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/4804a295eae5c37717d418ec1bc6d9908399721e))
* remove hamburger border-radius ([#9](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/9)) ([3000933](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/300093370a74858bce7018bdafb227296ff42cca))
* remove link from highlight grid elements ([6b59920](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/6b5992093b9f09a674a98106e92ff95820828e07))
* temporary remove english i18n locale ([131a052](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/131a052254f739bbb7f7fa492165d2f1066014d9))
* update ([5abdb37](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/5abdb3789c42b65364d12ddacd28fe05078c6983))
* update ([e33372f](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/e33372f9780b548153a182ff505c089a2c93ccf1))
* update ([2cc59c3](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/2cc59c36f08a3cf7f0533957f0f8d097da723475))
* update ([c821bd4](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/c821bd42d8c5584be7622fc3dbb563adccc5730c))
* update ([7c47d8d](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/7c47d8d7c87e3555de6984055458c5aeb0ede57f))
* update ([5337107](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/53371071aa44616245e8f1527c92fa5e497126a3))
* update ([7504801](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/7504801f204e1f0850107988767972d16cc5d728))
* update ([c6449a2](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/c6449a2c80ce81be72c7361a2e8992662faadfe5))
* update ([5462266](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/5462266988f50ce8dca13d0588b98a32ec35a78d))
* update ([ac91792](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/ac91792e12f09d1f0b16311bfe95d6da9cc42365))
* update favicon ([6af5fc2](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/6af5fc2032b7b211e257ca2e58874cec9272d65c))
* update payload import map ([#4](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/4)) ([9028d92](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/9028d92f4bcbf838d7695c7cef820a0a8e4eea12))
* use cache for page fetch ([#16](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/16)) ([98e475a](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/98e475a41789fb8586a0bc29c5d22ac807be126d))
* use edge runtime in middleware ([1b635c9](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/1b635c975e1ef5ff9c2798c5b7d5925ecd3cbe82))
* use text-base instead of text-md ([7713572](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/771357296c1bbb9c5f3e9783615b1c60813e2652))


### Reverts

* "chore(main): release 1.0.0 ([#1](https://github.com/hansainvest-real/SICORE_Home_Mono/issues/1))" ([7e27828](https://github.com/hansainvest-real/SICORE_Home_Mono/commit/7e27828b4117a544f841aa5e12a59cf1f88e7eb2))
