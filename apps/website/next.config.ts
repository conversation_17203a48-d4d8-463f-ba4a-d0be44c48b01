import nextBundleAnalyzer from "@next/bundle-analyzer";
import { withPayload } from "@payloadcms/next/withPayload";
import { PHASE_PRODUCTION_BUILD } from "next/constants";
import nextIntlPlugin from "next-intl/plugin";

import type { NextConfig } from "next";

const envOrDefault = (envKey: any, fallback: string) => {
	const input = process.env[envKey];

	if (typeof input !== "string" || input.length === 0) {
		return fallback;
	}

	return input;
};

const configFn = async (phase: string) => {
	const buildVersion = envOrDefault("BUILD_VERSION", "v0.0.0-development");
	const buildCommit = envOrDefault("BUILD_COMMIT", "development");

	let config: NextConfig = {
		output: "standalone",
		pageExtensions: ["js", "jsx", "ts", "tsx"],
		eslint: { ignoreDuringBuilds: true },
		poweredByHeader: false,
		// cacheHandler:
		// 	phase === PHASE_PRODUCTION_BUILD
		// 		? require.resolve("./src/cache-handler.mjs")
		// 		: undefined,
		generateBuildId: () => buildVersion,
		webpack: (config, { buildId, webpack }) => {
			config.plugins.push(
				new webpack.DefinePlugin({
					__CONFIG_BUILD_ID: JSON.stringify(buildId),
				}),
			);

			return config;
		},
		skipTrailingSlashRedirect: true,
		experimental: {
			useCache: true,
		},
	};

	// Add the Bundle Analyzer for Production Builds when the correct environment variable is set.
	// The additional environment variable gate is added to the condition, because '@next/bundle-analyzer' is listed
	// as 'devDependency' and is therefore not available in all environments.
	if (phase === PHASE_PRODUCTION_BUILD && process.env.NEXT_ANALYZE === "true") {
		config = nextBundleAnalyzer({
			enabled: process.env.NEXT_ANALYZE === "true",
		})(config);
	}

	// Determine the deployment environment based on the build version.
	let deployEnvironment;
	if (buildVersion === "v0.0.0-development") {
		deployEnvironment = "development";
	} else if (buildVersion.startsWith("v0.0.0-")) {
		deployEnvironment = "staging";
	} else {
		deployEnvironment = "production";
	}

	config = nextIntlPlugin()(config);

	// Apply PayloadCMS plugin.
	config = withPayload(config);

	return config;
};

export default configFn;
