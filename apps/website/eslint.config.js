import baseConfig from "@abinnovision/eslint-config-base";
import reactConfig from "@abinnovision/eslint-config-react";
import typescriptConfig from "@abinnovision/eslint-config-typescript";
import pluginNext from "@next/eslint-plugin-next";
import globals from "globals";

/** @type {import("@types/eslint").Linter.Config[]} */
export default [
	...baseConfig,
	...typescriptConfig,
	...reactConfig,
	{ files: ["**/*.js"], languageOptions: { globals: globals.node } },
	{
		files: ["**/*.{jsx,tsx}"],
		plugins: {
			"@next/next": pluginNext,
		},
		rules: {
			...pluginNext.configs["recommended"].rules,
		},
	},
];
