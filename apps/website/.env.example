# Connection string for the MongoDB database.
# Se here: https://www.mongodb.com/docs/manual/reference/connection-string/
DATABASE_URI=mongodb://127.0.0.1:27017/payload

# Must be a random string.
PAYLOAD_SECRET=<>

# The base URL of the application.
# This is used to generate absolute URLs.
APP_BASE_URL=http://localhost:3000

# If the crawling of the website should be disallowed (done through robots.txt).
# In production, this should be set to 'false' to allow crawling.
APP_DISALLOW_CRAWLING=true

# Configuration for the S3 bucket to store user-media.
S3_BUCKET=payload
S3_ENDPOINT=http://localhost:9000
S3_ACCESS_KEY_ID=C1FBFD0D-1CAD-4796-8883-C0FE2B0F7317
S3_SECRET_ACCESS_KEY=D04259D5-1BAB-4615-BE2A-B79A2B01B6AA
S3_REGION=minio

# Configuration for the Typesense database.
TYPESENSE_ENDPOINT=http://localhost:8108
TYPESENSE_API_KEY=root

VALKEY_URL=redis://localhost:6379
# Or, when using sentinels:
# VALKEY_SENTINELS=redis-sentinel-1:26379,redis-sentinel-2:26379
# VALKEY_SENTINEL_NAME=mymaster