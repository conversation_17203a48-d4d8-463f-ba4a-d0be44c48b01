import { resolve } from "path";
import react from "@vitejs/plugin-react";
import { defineConfig } from "vitest/config";

export default defineConfig({
	plugins: [react()],
	test: {
		name: "@internal/app-website#unit",
		environment: "jsdom",
		include: ["src/**/*.spec.{ts,tsx}"],
	},
	resolve: {
		alias: {
			"@": resolve(__dirname, "./src"),
			"@payload-config": resolve(__dirname, "./src/payload/config.ts"),
		},
	},
});
