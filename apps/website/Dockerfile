ARG NODE_VERSION=22.14.0

# syntax=docker/dockerfile:1.10.0
FROM node:$NODE_VERSION-alpine AS builder

WORKDIR /app
RUN apk add jq
COPY . .

# Build a skeleton of the project with only the necessary files.
# This is done to avoid copying the whole project into the image.
RUN corepack enable
RUN yarn dlx turbo@$(jq ".devDependencies.turbo" -r package.json) prune --scope="@internal/app-website" --docker

FROM node:$NODE_VERSION-alpine AS installer

RUN apk update
RUN apk add --no-cache libc6-compat

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

WORKDIR /app

COPY --from=builder /app/out/json/ .

RUN corepack enable
RUN yarn install --immutable

ARG build_version
ARG build_commit
ENV BUILD_VERSION=$build_version
ENV BUILD_COMMIT=$build_commit

COPY --from=builder /app/out/full/ .

RUN yarn turbo build --filter=@internal/app-website

FROM node:$NODE_VERSION-alpine AS runner

WORKDIR /app

COPY --from=installer --chown=node:node /app/apps/website/.next/standalone ./
COPY --from=installer --chown=node:node /app/apps/website/.next/static ./apps/website/.next/static
COPY --from=installer --chown=node:node /app/apps/website/public ./apps/website/public

# Use an user with a non-root UID/GID to run the application.
USER node
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV NEXT_SHARP_PATH=/app/node_modules/sharp

EXPOSE 3000
CMD ["node", "apps/website/server.js"]
