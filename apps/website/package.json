{"$schema": "https://json.schemastore.org/package.json", "name": "@internal/app-website", "version": "1.9.14", "private": true, "license": "proprietary", "type": "module", "scripts": {"build": "next build", "format:check": "prettier --check '{{src,test,cypress}/**/*,*}.{js,jsx,ts,tsx,json,json5,yml,yaml,md}'", "format:fix": "prettier --write '{{src,test,cypress}/**/*,*}.{js,jsx,ts,tsx,json,json5,yml,yaml,md}'", "lint:check": "eslint '{{src,test,cypress}/**/*,*}.{js,jsx,ts,tsx}'", "lint:fix": "eslint '{{src,test,cypress}/**/*,*}.{js,jsx,ts,tsx}' --fix", "start": "next start", "start:dev": "next dev --experimental-https", "generate": "yarn payload:generate", "payload:generate": "mkdir -p generated/payload/ & payload generate:types", "test-unit": "", "test-unit:watch": "vitest --watch"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{js,jsx,ts,tsx,json,json5,yaml,yml,md}": ["prettier --write"]}, "browserslist": "defaults, not ie <= 11", "prettier": "@abinnovision/prettier-config", "dependencies": {"@atelier-disko/payload-lexical-react-renderer": "^1.2.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@internal/cache-handler": "workspace:^", "@internal/jobs-fetcher": "workspace:^", "@neshca/cache-handler": "^1.9.0", "@payloadcms/db-mongodb": "^3.40.0", "@payloadcms/live-preview-react": "^3.40.0", "@payloadcms/next": "^3.40.0", "@payloadcms/payload-cloud": "^3.40.0", "@payloadcms/plugin-redirects": "^3.40.0", "@payloadcms/plugin-seo": "^3.40.0", "@payloadcms/richtext-lexical": "^3.40.0", "@payloadcms/storage-s3": "^3.40.0", "@payloadcms/ui": "^3.40.0", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-query": "^5.67.2", "@types/node": "22.13.10", "@zapal/payload-lexical-react": "^1.8.0", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "date-fns": "^4.1.0", "deepmerge-ts": "^7.1.5", "graphql": "^16.11.0", "i18n-iso-countries": "^7.14.0", "keen-slider": "^6.8.6", "lucide": "^0.513.0", "lucide-react": "^0.513.0", "motion": "^12.16.0", "next": "15.3.2", "next-intl": "^3.26.5", "path-to-regexp": "^8.2.0", "payload": "^3.40.0", "plaiceholder": "^3.0.0", "react": "19.1.0", "react-animate-height": "^3.2.3", "react-countup": "^6.5.3", "react-dom": "19.1.0", "react-svg-worldmap": "^2.0.0-alpha.16", "react-use": "^17.6.0", "schema-dts": "^1.1.5", "server-only": "^0.0.1", "sharp": "^0.34.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.0.12", "traverse": "^0.6.11", "typescript": "^5.8.2", "typesense": "^2.0.3", "usehooks-ts": "^3.1.1", "zod": "^3.25.51"}, "devDependencies": {"@abinnovision/eslint-config-base": "^2.2.0", "@abinnovision/eslint-config-react": "^2.2.0", "@abinnovision/eslint-config-typescript": "^2.2.1", "@abinnovision/prettier-config": "^2.1.3", "@next/bundle-analyzer": "15.3.2", "@next/eslint-plugin-next": "15.3.2", "@tailwindcss/postcss": "^4.0.12", "@tailwindcss/typography": "^0.5.16", "@types/gtag.js": "^0.0.20", "@types/jsdom": "^21.1.7", "@types/prop-types": "^15.7.14", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@types/traverse": "^0.6.37", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-config-next": "15.3.2", "globals": "^16.0.0", "jsdom": "^26.0.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "sass": "^1.85.1", "sort-package-json": "^3.0.0", "vitest": "^3.0.8", "vitest-mock-extended": "^3.0.1"}}