{"compilerOptions": {"target": "ES2023", "lib": ["dom", "dom.iterable", "esnext"], "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/gen/*": ["./generated/*"], "@payload-config": ["./src/payload/config.ts"]}, "allowJs": true}, "include": ["next-env.d.ts", "src/**/*.ts", "src/**/*.tsx", "generated/**/*.ts", ".next/types/**/*.ts", "shims.d.ts", "tailwind.config.ts", "next.config.ts"], "exclude": ["node_modules"]}