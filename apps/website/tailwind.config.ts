import forms from "@tailwindcss/forms";
import typography from "@tailwindcss/typography";
import defaultTheme from "tailwindcss/defaultTheme";
import plugin from "tailwindcss/plugin";

import type { Config } from "tailwindcss";

export default {
	content: ["./src/**/*.{js,jsx,mdx,ts,tsx}"],
	darkMode: "selector",
	theme: {
		fontSize: {
			xs: ["0.75rem", { lineHeight: "1rem" }],
			sm: ["0.875rem", { lineHeight: "1.5rem" }],
			base: ["1rem", { lineHeight: "1.75rem" }],
			lg: ["1.125rem", { lineHeight: "1.75rem" }],
			xl: ["1.25rem", { lineHeight: "2rem" }],
			"2xl": ["1.5rem", { lineHeight: "2.25rem" }],
			"3xl": ["1.75rem", { lineHeight: "2.25rem" }],
			"4xl": ["2rem", { lineHeight: "2.5rem" }],
			"5xl": ["2.5rem", { lineHeight: "3rem" }],
			"6xl": ["3rem", { lineHeight: "3.5rem" }],
			"7xl": ["4rem", { lineHeight: "4.5rem" }],
		},
		extend: {
			fontFamily: {
				serif: [
					"var(--font-din-next-slab-pro)",
					...defaultTheme.fontFamily.serif,
				],
				sans: [
					"var(--font-helvetica-neue-lt-std)",
					...defaultTheme.fontFamily.sans,
				],
			},
			colors: {
				primary: {
					DEFAULT: "#6543C7",
				},
			},
			borderRadius: {
				"4xl": "2.5rem",
			},
			keyframes: {
				"slide-left": {
					from: { transform: "translateX(0)" },
					to: { transform: "translateX(-100%)" },
				},
			},
			animation: {
				"slide-left": "slide-left 20s linear infinite",
			},
		},
	},
	plugins: [
		typography,
		forms,
		plugin(({ addVariant }) => {
			addVariant("color-inverted", [".--inverted &", "&.--inverted"]);
		}),
	],
} satisfies Config;
