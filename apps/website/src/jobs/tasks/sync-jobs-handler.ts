import { fetchAllJobs, fetchJobDetailed } from "@internal/jobs-fetcher";
import * as dfns from "date-fns";

import type { TaskHandler } from "payload";

export const syncJobsHandler: TaskHandler<"sync-jobs"> = async (ctx) => {
	// Define the current time as a reference.
	// This is mainly used for the "lastSeen" property.
	const now = new Date();

	// Define the date after which jobs should be deleted.
	const deleteAfter = dfns.add(now, { days: 7 });

	// Fetch all jobs in the compact format.
	const remoteJobs = await fetchAllJobs();

	// Fetch all existing jobs within the database.
	const existingJobs = await ctx.req.payload.find({
		collection: "jobs",
	});

	// Create an array of all active jobs.
	const activeJobs: string[] = [];

	// Iterate over the remote jobs and check if they already exist in the database.
	for (let remoteJob of remoteJobs) {
		const existingJob = existingJobs.docs.find((it) => it.id === remoteJob.id);

		// If the job does not exist, create it.
		if (!existingJob) {
			// Fetch the detailed information for the job.
			const detailedJob = await fetchJobDetailed(remoteJob);

			await ctx.req.payload.create({
				collection: "jobs",
				data: {
					id: detailedJob.id,

					// The other properties are computed within hooks.

					// Define the data of the job.
					data: detailedJob as any,
					lastDataUpdate: now.toISOString(),

					// Set the job as active and the last seen time to the current time.
					lastSeen: now.toISOString(),
				},
			});
		} else {
			// Define if a full update is required.
			const fullUpdateThreshold = dfns.sub(now, { hours: 6 });

			// Check if a full update is required.
			const needsFullUpdate = dfns.isBefore(
				existingJob.lastDataUpdate ?? 0,
				fullUpdateThreshold,
			);

			if (needsFullUpdate) {
				// Fetch the detailed information for the job.
				const detailedJob = await fetchJobDetailed(remoteJob);

				// Update the job in the database.
				await ctx.req.payload.update({
					collection: "jobs",
					where: { id: { equals: existingJob.id } },
					data: {
						// Define the data of the job.
						data: detailedJob as any,
						lastDataUpdate: now.toISOString(),
					},
				});
			}
		}

		// The job is active, so we push it to the active jobs array.
		activeJobs.push(remoteJob.id);
	}
	// Update all the active jobs in bulk.
	if (activeJobs.length > 0) {
		await ctx.req.payload.update({
			collection: "jobs",
			where: {
				id: { in: activeJobs },
				lastSeen: { not_equals: now.toISOString() },
			},
			data: {
				// Set the job as active and the last seen time to the current time.
				lastSeen: now.toISOString(),
			},
		});
	}

	// Build the list of jobs that are no longer active.
	const inactiveJobs = existingJobs.docs.filter(
		(it) => !activeJobs.includes(it.id),
	);

	// If there are any inactive jobs, we need to update their status to inactive.
	if (inactiveJobs.length > 0) {
		// Check if there are any jobs that are inactive and should be deleted.
		const forDeletion = inactiveJobs.filter((it) =>
			dfns.isAfter(it.lastSeen, deleteAfter),
		);

		// If there are any jobs that should be deleted, we need to delete them.
		if (forDeletion.length > 0) {
			await ctx.req.payload.delete({
				collection: "jobs",
				where: { id: { in: forDeletion.map((it) => it.id) } },
			});
		}
	}

	return { output: undefined };
};
