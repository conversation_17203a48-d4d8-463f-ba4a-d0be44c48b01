import traverse from "traverse";

import { createIndexBlockDataResolversMapping } from "@/blocks/data-resolvers-mapping";
import { payloadBlockDataResolversExtension } from "@/utils/payload-block-data-resolver/context-extension";
import { getPayloadDocument } from "@/utils/payload-block-utils";

import type { BaseBlock } from "@/blocks/utils/types";
import type { PayloadBlockContext } from "@/utils/payload-block";
import type {
	PayloadBlockDataResolverFn,
	PayloadBlockDataResolversResult,
} from "@/utils/payload-block-data-resolver/types";

interface DataResolverExecutions {
	[identity: string]: {
		resolver: PayloadBlockDataResolverFn<any, any>;
		block: BaseBlock & Record<string, any>;
	};
}

const dataResolversIndex = createIndexBlockDataResolversMapping();

/**
 * Builds the executions for the given data.
 * The data must be the root structure of a document.
 *
 * @param data Root data of a document.
 */
const buildBlockExecutions = (data: any): DataResolverExecutions => {
	// Registry for all the executions.
	const executions: DataResolverExecutions = {};

	traverse(data).forEach((element) => {
		// Check if this is an object.
		if (
			typeof element !== "object" ||
			element === null ||
			element === undefined
		) {
			return;
		}

		if (!element.blockType || !element.id) {
			return;
		}

		// Check if there is a data resolver available.
		const dataResolver = dataResolversIndex[element.blockType as string];
		if (!dataResolver) {
			return;
		}

		const identity = `${element.blockType}$${element.id}`;

		// Check if the resolver is already executed.
		if (executions[identity]) {
			return;
		}

		executions[identity] = {
			resolver: dataResolver.fn,
			block: element,
		};
	});

	// The document itself is a special block. It represents an document shell.

	return executions;
};

/**
 * Builds the document execution for the given context and data. Data must be the root data of a document.
 * This will build the execution for the document shell itself.
 *
 * @param ctx The context for the current document.
 * @param data The root data of the document.
 */
const buildDocumentExecutions = (
	ctx: PayloadBlockContext,
	data: any,
): DataResolverExecutions => {
	// This is a special block naming.
	const type = `${ctx.document.collection}-document-shell`;

	// If there is no resolver for the document, we can skip it.
	if (!dataResolversIndex[type]) {
		return {};
	}

	// Get the resolver.
	const resolver = dataResolversIndex[type];

	// Load the document.
	const document = getPayloadDocument(ctx);

	return {
		[`${ctx.document.collection}-document-shell$${document.id}`]: {
			resolver: resolver.fn,
			block: data,
		},
	};
};

/**
 * Builds the DataResolverExecutions for the given data which only focuses on blocks.
 *
 * @param data Root data of a document.
 */
export const buildExecutionsForBlocks = (data: any): DataResolverExecutions =>
	buildBlockExecutions(data);

/**
 * Builds the DataResolverExecutions for the given data which only focuses on documents.
 *
 * @param ctx
 * @param data
 */
export const buildExecutionsForDocuments = (
	ctx: PayloadBlockContext,
	data: any,
): DataResolverExecutions => buildDocumentExecutions(ctx, data);

/**
 * Runs all executions and returns the result.
 * @param ctx The context to run the executions on.
 * @param executions The executions to run.
 */
export const runDataResolver = async (
	ctx: PayloadBlockContext,
	executions: DataResolverExecutions,
): Promise<PayloadBlockDataResolversResult> => {
	const result: PayloadBlockDataResolversResult = {};

	await Promise.all(
		Object.entries(executions).map(async (entry) => {
			const [identity, value] = entry;

			result[identity] = await value.resolver({
				block: value.block,
				ctx,
			});
		}),
	);

	return result;
};

/**
 * Runs all given DataResolverExecutions on the given context.
 *
 * @param ctx The context to run the executions on.
 * @param executions The executions to run.
 */
export const runDataResolversOnContext = async (
	ctx: PayloadBlockContext,
	executions: DataResolverExecutions,
): Promise<void> => {
	// Apply the result to the context.
	payloadBlockDataResolversExtension.set(
		ctx,
		await runDataResolver(ctx, executions),
	);
};
