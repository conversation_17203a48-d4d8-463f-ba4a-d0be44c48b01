import type { BaseBlock } from "@/blocks/utils/types";
import type { PayloadBlockContext } from "@/utils/payload-block";

/**
 * Defines the arguments for a data resolver function.
 */
export interface PayloadBlockDataResolverArgs<T extends BaseBlock> {
	block: T;
	ctx: PayloadBlockContext;
}

/**
 * Defines a callable data resolver function for a block.
 */
export type PayloadBlockDataResolverFn<T extends BaseBlock, R> = (
	args: PayloadBlockDataResolverArgs<T>,
) => Promise<R> | R;

/**
 * Defines a data resolver for a block.
 */
export interface PayloadBlockDataResolver<T extends BaseBlock, R = any> {
	/**
	 * The slug of the block.
	 */
	slug: string;

	/**
	 * The function to execute for the data resolver.
	 */
	fn: PayloadBlockDataResolverFn<T, R>;
}

export interface PayloadBlockDataResolversResult {
	[key: string]: any;
}
