import type { PayloadDocumentExtended } from "@/utils/payload-document/types";
import type { CollectionSlug, TypedCollection } from "payload";

type ExtendedTypedCollection = {
	[S in CollectionSlug]: TypedCollection[S] & PayloadDocumentExtended;
};

/**
 * Returns if the given document is of the given collection type.
 *
 * @param data The document to check.
 * @param collection Collection name to check against.
 */
export const isPayloadDocumentType = <S extends CollectionSlug>(
	data: PayloadDocumentExtended,
	collection: S,
): data is ExtendedTypedCollection[S] => {
	return data.$collectionName === collection;
};
