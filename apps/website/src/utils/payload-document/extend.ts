import type {
	PayloadDocumentExtended,
	PayloadDocument,
} from "@/utils/payload-document/types";
import type { CollectionSlug } from "payload";

interface ExtendPayloadDocumentOpts {
	collection: CollectionSlug;
}

/**
 * Extends the given Payload document with the collection name.
 *
 * @param data
 * @param opts
 */
export const extendPayloadDocument = <T extends PayloadDocument>(
	data: T,
	opts: ExtendPayloadDocumentOpts,
): T & PayloadDocumentExtended => {
	return {
		...data,
		$collectionName: opts.collection,
	};
};
