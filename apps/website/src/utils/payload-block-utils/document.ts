import { createPayloadBlockContextExtension } from "@/utils/payload-block";

import type { PayloadBlockContext } from "@/utils/payload-block";
import type { CollectionSlug, TypedCollection } from "payload";

type DocumentPartial = {
	id: string;
	collection: string;
} & Record<string, any>;

const _contextExtension =
	createPayloadBlockContextExtension<DocumentPartial>("document");

/**
 * Initializes the document on the given context.
 *
 * @param ctx The context to initialize the document on.
 * @param data The document data.
 */
export const initPayloadDocument = (
	ctx: PayloadBlockContext,
	data: DocumentPartial,
): void => {
	_contextExtension.set(ctx, data);
};

/**
 * Provides the current document.
 *
 * @param ctx The context to get the document from.
 * @param collection The collection to get the document from.
 * If provided, the document is type-checked.
 */
export const getPayloadDocument = <S extends CollectionSlug>(
	ctx: PayloadBlockContext,
	collection?: S,
): TypedCollection[S] => {
	const data = _contextExtension.get(ctx);
	if (!data) {
		throw new Error("No document available");
	}

	if (collection && ctx.document.collection !== collection) {
		throw new Error(`Document is not of type ${collection}`);
	}

	return data as any as TypedCollection[S];
};

/**
 * Provides the ID of the current document.
 *
 * @param ctx The context to get the document from.
 */
export const getPayloadDocumentId = (ctx: PayloadBlockContext): string => {
	const data = _contextExtension.get(ctx);
	if (!data) {
		throw new Error("No document available");
	}

	return data.id;
};
