import { getPayloadCollectionMapping } from "@/utils/payload-collections-mapping";
import { lookupPayloadDocumentSlugs } from "@/utils/payload-link-lookup";

import type { <PERSON>Field } from "@/gen/payload/types";
import type { <PERSON> } from "@/navigation";
import type { PayloadBlockContext } from "@/utils/payload-block";
import type { ComponentPropsWithoutRef } from "react";

type ResolvedLink = Pick<
	ComponentPropsWithoutRef<typeof Link>,
	"href" | "target" | "onClick"
>;

interface ResolvePayloadLinkOpts {
	ctx: PayloadBlockContext;
	link: LinkField | undefined;
}

interface BuildPayloadLinkOpts {
	ctx: PayloadBlockContext;
	slug?: string;
	searchParams?: Record<string, any>;
}

interface IsAvailablePayloadLinkOpts {
	/**
	 * The link to check against.
	 */
	link: LinkField | undefined;

	/**
	 * If true, it's required to have a label present.
	 */
	withLabel?: boolean;
}

/**
 * Resolves the action to execute for an action.
 *
 * @param action The action to resolve.
 * @returns The function to execute or undefined if the action is not supported.
 */
const resolveActionFn = (
	action: Required<LinkField["action"]>,
): (() => void) | undefined => {
	if (action === "cookiebot-renew") {
		return () => {
			(window as any).Cookiebot.renew();
		};
	}

	return undefined;
};

// eslint-disable-next-line complexity
export const resolvePayloadLink = (
	opts: ResolvePayloadLinkOpts,
): ResolvedLink => {
	if (!opts.link) {
		return {
			href: "/",
			target: "_self",
		};
	}

	let target: ResolvedLink["target"] = "_self";
	if (opts.link.newTab) {
		target = "_blank";
	}

	const fallbackLink: ResolvedLink = {
		href: "/",
		target,
	};

	if (opts.link.type === "custom") {
		return {
			href: opts.link.url ?? "/",
			target,
		};
	} else if (opts.link.type === "reference") {
		let targetDocumentId: string | undefined;
		if (typeof opts.link.reference?.value === "string") {
			targetDocumentId = opts.link.reference.value;
		} else if (opts.link.reference?.value?.id) {
			targetDocumentId = opts.link.reference.value.id;
		}

		// If the target could not be resolved, return the fallback link.
		if (!targetDocumentId) {
			return fallbackLink;
		}

		const lookUpSlug = lookupPayloadDocumentSlugs({
			ctx: opts.ctx,
			pageId: targetDocumentId,
		});

		// If the lookup is not successful, return the fallback link.
		if (!lookUpSlug) {
			return fallbackLink;
		}

		const localizedSlug = lookUpSlug[opts.ctx.locale];
		if (!localizedSlug) {
			return fallbackLink;
		}

		let targetHref = localizedSlug;

		// If we're in preview mode, we need to add the preview path.
		if (opts.ctx.isPreview) {
			targetHref = `/-preview${targetHref}`;
		}

		if (
			opts.link.reference?.relationTo &&
			opts.link.reference?.relationTo !== "pages"
		) {
			const mapping = getPayloadCollectionMapping(
				opts.ctx,
				opts.link.reference?.relationTo,
			);
			if (mapping) {
				targetHref = mapping.resolvers[opts.ctx.locale].build({
					id: targetDocumentId,
					slug: localizedSlug,
				});
			}
		}

		return {
			href: targetHref,
			target,
		};
	} else if (opts.link.type === "same-page") {
		return {
			href: `#${opts.link.samePageIdentifier}`,
			target,
			onClick: (e) => {
				e.preventDefault();

				const elementQuery = `#${opts.link?.samePageIdentifier}`;

				// If the element is not found, do nothing.
				const element = document.querySelector(elementQuery);
				if (!element) {
					return;
				}

				element.scrollIntoView({
					behavior: "smooth",
					inline: "center",
					block: "center",
				});
			},
		};
	} else if (opts.link.type === "action") {
		// If there is no action defined, return the fallback link.
		if (!opts.link.action) {
			return fallbackLink;
		}

		return {
			href: "",
			onClick: (e) => {
				// This type only defines a click-handler.
				// Therefore, we always prevent the default.
				e.preventDefault();

				// Resolve the function of the action.
				const fn = resolveActionFn(opts.link?.action);

				// Call the action function and wrap it in a try-catch.
				if (fn) {
					try {
						fn();
						// eslint-disable-next-line unused-imports/no-unused-vars
					} catch (_) {}
				}
			},
		};
	}

	return fallbackLink;
};

/**
 * Builds an internal link for the given context.
 *
 * @param opts
 */
export const buildPayloadLink = (opts: BuildPayloadLinkOpts): string => {
	// Use the provided slug or use the current one.
	let link = opts.slug ?? opts.ctx.path;

	if (opts.ctx.isPreview) {
		link = `/-preview${link}`;
	}

	let searchParams: Record<string, string> = {};
	if (opts.ctx.searchParams) {
		searchParams = { ...opts.ctx.searchParams };
	}

	// Override the search params with the ones from the options.
	if (opts.searchParams) {
		searchParams = { ...searchParams, ...opts.searchParams };
	}

	// If there are search params, add them to the link.
	if (Object.keys(searchParams).length > 0) {
		const searchParamsString = new URLSearchParams(searchParams).toString();
		link += `?${searchParamsString}`;
	}

	return link;
};

/**
 * Checks if the given link is available.
 *
 * @param opts The options to check against.
 * @returns True if the link is available, false otherwise.
 */
export const isAvailablePayloadLink = (
	opts: IsAvailablePayloadLinkOpts,
): boolean => {
	if (!opts.link) {
		return false;
	}

	if (opts.withLabel && !opts.link.label) {
		return false;
	}

	if (opts.link.type === "custom" && !opts.link.url) {
		return false;
	}

	if (opts.link.type === "same-page" && !opts.link.samePageIdentifier) {
		return false;
	}

	if (opts.link.type === "action" && !opts.link.action) {
		return false;
	}

	return true;
};
