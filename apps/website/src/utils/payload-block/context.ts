import type { AvailableLocale } from "@/i18n";

export interface PayloadBlockContextBase {
	/**
	 * Reference to the currently showing document.
	 * This may not be an unique identifier.
	 */
	document: {
		/**
		 * Name of the collection.
		 */
		collection: string;

		/**
		 * Identifier of the document. This describes the value of one field in the document.
		 * This is used to identify the document.
		 */
		identifier: { field: string; value: string };
	};

	/**
	 * Path of the current page. This is localized with the current locale.
	 * This will always start with a forward slash, but does not contain the locale prefix.
	 */
	path: string;

	/**
	 * The current locale of the page
	 */
	locale: AvailableLocale;

	/**
	 * The base URL of the current instance.
	 */
	baseUrl: string;

	/**
	 * The search params of the current page.
	 */
	searchParams: Record<string, any>;

	/**
	 * If running in preview mode.
	 */
	isPreview: boolean;
}

/**
 * Interface for the Payload block context.
 */
export type PayloadBlockContext = PayloadBlockContextBase & {
	[key: `__ctx_extension_${string}`]: any;
};

export interface PayloadBlockContextExtension<T> {
	set: (ctx: PayloadBlockContext, value: T) => void;
	get: (ctx: PayloadBlockContext) => T | undefined;
}

/**
 * Creates a new extension for the payload block context.
 *
 * @param name The name of the extension.
 */
export const createPayloadBlockContextExtension = <T>(
	name: string,
): PayloadBlockContextExtension<T> => {
	const symbol = `__ctx_extension_${name}` as const;

	return {
		set: (ctx, v) => {
			ctx[symbol] = v;
		},
		get: (ctx) => {
			return ctx[symbol];
		},
	};
};

/**
 * Creates a new payload block context with the given parent.
 * This is based on the prototype chain.
 *
 * @param parent The parent context.
 */
export const createChildrenPayloadBlockContext = (
	parent: PayloadBlockContext,
): PayloadBlockContext => Object.create(parent);
