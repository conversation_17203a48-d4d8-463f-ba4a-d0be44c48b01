import { createCollectionsMappingIdentity } from "./utils";
import { resolveCollectionMapping } from "@/utils/payload-collections-mapping/resolver";

import type {
	PayloadCollectionMapping,
	PayloadCollectionMappingResolved,
} from "@/utils/payload-collections-mapping/types";

// This is a dumb cache for the collection mappings.
const _CACHE: Record<string, PayloadCollectionMappingResolved[]> = {};

/**
 * Resolves the collection mappings to a functional resolver.
 *
 * @param input
 */
export const getCachedResolvedCollectionMappings = async (
	input: PayloadCollectionMapping[],
): Promise<PayloadCollectionMappingResolved[]> => {
	const id = await createCollectionsMappingIdentity(input);

	if (_CACHE[id]) {
		return _CACHE[id];
	}

	const resolved = await Promise.all(
		input.map((it) => resolveCollectionMapping(it)),
	);

	_CACHE[id] = resolved;

	return resolved;
};
