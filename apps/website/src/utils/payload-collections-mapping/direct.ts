import { getCachedResolvedCollectionMappings } from "@/utils/payload-collections-mapping/cache";
import { fetchCollectionMappings } from "@/utils/payload-collections-mapping/fetch";

import type { PayloadCollectionMappingResolved } from "@/utils/payload-collections-mapping/types";

/**
 * Provides the collection mappings with functional resolvers.
 */
export const getResolvedCollectionMappings = async (): Promise<
	PayloadCollectionMappingResolved[]
> => {
	// Fetch the collection mappings.
	const mapping = await fetchCollectionMappings();

	// Resolve the collection mappings from the cache.
	return await getCachedResolvedCollectionMappings(mapping);
};
