import * as ptr from "path-to-regexp";

import type {
	PayloadCollectionMapping,
	PayloadCollectionMappingResolved,
} from "@/utils/payload-collections-mapping/types";
import type { ParamData } from "path-to-regexp";

/**
 * Resolves the collection mapping to a functional resolver.
 *
 * @param input The input collection mapping.
 */
export const resolveCollectionMapping = (
	input: PayloadCollectionMapping,
): PayloadCollectionMappingResolved => {
	return {
		...input,
		resolvers: Object.fromEntries(
			Object.entries(input.path).map(([locale, path]) => {
				const matchFn = ptr.match(path);
				const buildFn = ptr.compile(path);

				return [
					locale,
					{
						match: (path: string) => {
							const match = matchFn(path);

							if (!match) {
								return false;
							}

							return match.params;
						},
						build: (params: ParamData) => buildFn(params),
					},
				];
			}),
		),
	};
};
