import { createHash } from "node:crypto";

import type { PayloadCollectionMapping } from "@/utils/payload-collections-mapping/types";

/**
 * Creates an identity for the collection mapping array.
 *
 * @param input The input array.
 */
export const createCollectionsMappingIdentity = async (
	input: PayloadCollectionMapping[],
): Promise<string> => {
	const hash = createHash("sha1");

	for (let entry of input) {
		hash.update(`${entry.collection}-${JSON.stringify(entry.path)}`);
	}

	return hash.digest("base64");
};
