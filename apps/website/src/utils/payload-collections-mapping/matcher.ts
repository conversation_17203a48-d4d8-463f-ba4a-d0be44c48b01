import type { PayloadCollectionMappingResolved } from "@/utils/payload-collections-mapping/types";

interface MatchOpts {
	path: string;
	locale: string;
	mappings: PayloadCollectionMappingResolved[];
}

type MatchResult =
	| false
	| {
			mapping: PayloadCollectionMappingResolved;
			identifier: { field: string; value: string };
	  };

/**
 * Matches against a list of collection mappings. The first match is returned.
 *
 * @param opts The options to match against.
 */
export const matchPayloadCollectionMappings = async (
	opts: MatchOpts,
): Promise<MatchResult> => {
	// There is a special case when matching, the "/" path.
	// It MUST always use the 'pages' collection.
	if (opts.path === "/") {
		const pagesMapping = opts.mappings.find((it) => it.collection === "pages");
		if (pagesMapping) {
			return {
				mapping: pagesMapping,
				identifier: { field: "slug", value: "" },
			};
		}
	}

	// For the mappings, the order is important.
	for (let mapping of opts.mappings) {
		// Try to resolve the matcher function, continue of the locale is not available.
		const matcherFn = mapping.resolvers[opts.locale].match;
		if (!matcherFn) {
			continue;
		}

		// Run the matcher function and continue if no match is found.
		const match = matcherFn(opts.path);
		if (!match) {
			continue;
		}

		const firstPair = Object.entries(match)[0];
		if (!firstPair) {
			continue;
		}

		let field = firstPair[0];
		let value = firstPair[1];
		if (!field || !value) {
			continue;
		}

		// If the value is an array, join it with a slash.
		if (Array.isArray(value)) {
			value = value.join("/");
		}

		return {
			mapping: mapping,
			identifier: { field, value },
		};
	}

	return false;
};
