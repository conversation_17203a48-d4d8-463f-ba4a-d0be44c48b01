import { contextExtension } from "@/utils/payload-collections-mapping/context";
import { resolveCollectionMapping } from "@/utils/payload-collections-mapping/resolver";

import type { PayloadBlockContext } from "@/utils/payload-block";
import type { PayloadCollectionMappingResolved } from "@/utils/payload-collections-mapping/types";
import type { CollectionSlug } from "payload";

export const getPayloadCollectionMapping = (
	ctx: PayloadBlockContext,
	collection: CollectionSlug,
): PayloadCollectionMappingResolved | undefined => {
	// Fetch the collection mappings.
	const mapping = contextExtension
		.get(ctx)
		?.find((it) => it.collection === collection);

	if (!mapping) {
		return undefined;
	}

	return resolveCollectionMapping(mapping);
};
