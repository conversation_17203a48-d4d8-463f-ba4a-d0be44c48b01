import { contextExtension } from "@/utils/payload-collections-mapping/context";
import { fetchCollectionMappings } from "@/utils/payload-collections-mapping/fetch";

import type { PayloadBlockContext } from "@/utils/payload-block";

export const initPayloadCollectionMappingsOnContext = async (
	ctx: PayloadBlockContext,
) => {
	const mappings = await fetchCollectionMappings();

	contextExtension.set(ctx, mappings);
};
