import payloadConfig from "@payload-config";
import { cacheTag } from "next/dist/server/use-cache/cache-tag";
import { getPayload } from "payload";

import { payloadCacheTags } from "@/utils/cache";

import type { PayloadCollectionMapping } from "@/utils/payload-collections-mapping/types";

/**
 * Create the collection mappings from the PayloadCMS global.
 * The return value of this function is cached.
 *
 * @returns The collection mappings.
 */
export const fetchCollectionMappings = async (): Promise<
	PayloadCollectionMapping[]
> => {
	"use cache";
	cacheTag(payloadCacheTags.all, payloadCacheTags.collectionMapping);

	const payload = await getPayload({ config: payloadConfig });

	// Fetch the collection mapping.
	const collectionsMapping = await payload.findGlobal({
		slug: "collections-mapping",
		locale: "all",
	});

	// Map the collection mapping to the desired format.
	return collectionsMapping.collections.map((it) => ({
		collection: it.collection,
		path: it.path as any,
	}));
};
