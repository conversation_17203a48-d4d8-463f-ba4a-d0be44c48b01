import type { ParamData } from "path-to-regexp";

/**
 * Describes a mapping for a single collection.
 */
export interface PayloadCollectionMapping {
	/**
	 * The name of the collection this mapping is for.
	 */
	collection: string;

	/**
	 * Path of the collection in all locales. E.g., /projects/:slug
	 */
	path: Record<string, string>;
}

export interface PayloadCollectionMappingResolved
	extends PayloadCollectionMapping {
	/**
	 * The resolved path of the collection in all locales.
	 */
	resolvers: Record<string, PayloadCollectionMappingResolvers>;
}

/**
 * Describes a mapping for a single collection with functional resolvers.
 */
export interface PayloadCollectionMappingResolvers {
	/**
	 * Matches the given path against the collection mapping.
	 *
	 * @param path The path to match.
	 * @returns The matched parameters or false if the path does not match.
	 */
	match: (path: string) => false | ParamData;

	/**
	 * Builds the path for the given parameters.
	 *
	 * @param params The parameters to build the path for.
	 * @returns The built path.
	 */
	build: (params: ParamData) => string;
}
