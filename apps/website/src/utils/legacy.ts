import traverse from "traverse";

export const adjustLegacyData = <T>(input: T): T => {
	traverse(input).forEach((element) => {
		if (
			typeof element !== "object" ||
			element === null ||
			element === undefined
		) {
			return;
		}

		if (!element.type || !element.reference) {
			return;
		}

		if (
			typeof element.reference === "object" &&
			element.reference.constructor.name === "ObjectId"
		) {
			element.reference = element.reference.toString();
		}
	});

	return input;
};
