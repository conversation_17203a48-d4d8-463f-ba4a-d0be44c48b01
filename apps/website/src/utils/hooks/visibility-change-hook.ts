import { useSyncExternalStore } from "react";

/**
 * Options for the useVisibilityChange hook.
 */
interface UseVisibilityChangeOpts {
	/**
	 * The visibility state to use on the server.
	 *
	 * Defaults to "visible".
	 */
	ssrVisibilityState?: DocumentVisibilityState;
}

/**
 * Hook to get the current visibility state of the document.
 */
export const useVisibilityChange = (
	opts?: UseVisibilityChangeOpts,
): DocumentVisibilityState => {
	return useSyncExternalStore(
		// Set up the listeners on the "visibilitychange" event.
		(cb) => {
			document.addEventListener("visibilitychange", cb);
			return () => document.removeEventListener("visibilitychange", cb);
		},
		// Get the current visibility state.
		// It defaults to "visible" if the visibility state is not supported.
		() => document.visibilityState ?? "visible",
		// On the server side we use the visibility state provided in the options or "visible" if not provided.
		() => opts?.ssrVisibilityState ?? "visible",
	);
};
