import { useState } from "react";
import { useEvent } from "react-use";

interface ConsentDetails {
	necessary: boolean;
	preferences: boolean;
	statistics: boolean;
	marketing: boolean;
}

/**
 * Representation of the consent result.
 * If the consent is not yet available, it's undefined.
 */
type ConsentResult =
	| undefined
	| {
			consented: false;
	  }
	| {
			consented: true;
			consent: ConsentDetails;
	  };

/**
 * Provides the consent details from Cookiebot.
 */
export const useCookiebotConsent = (): ConsentResult => {
	const [details, setDetails] = useState<ConsentResult>();

	const evaluateConsent = () => {
		const _rawConsented = (window as any).Cookiebot.consented;
		const _rawDetails = (window as any).Cookiebot.consent;

		if (_rawConsented === false || !_rawConsented) {
			setDetails({ consented: false });
		} else {
			setDetails({
				consented: true,
				consent: {
					necessary: _rawDetails.necessary,
					preferences: _rawDetails.preferences,
					statistics: _rawDetails.statistics,
					marketing: _rawDetails.marketing,
				},
			});
		}
	};

	useEvent(
		"CookiebotOnConsentReady",
		evaluateConsent,
		typeof window !== "undefined" ? window : undefined,
	);

	return details;
};

/**
 * Checks if the user has consented to the given type of Cookiebot consent.
 *
 * @param type The type of consent to check.
 * @returns True if the user has consented, false otherwise.
 * Undefined if the consent is not yet available.
 */
export const useCookiebotConsentForType = (
	type: keyof ConsentDetails,
): boolean | undefined => {
	const consent = useCookiebotConsent();

	// Undefined is a special case because the consent is not yet available.
	if (consent === undefined) {
		return undefined;
	}

	return consent.consented && consent.consent[type];
};
