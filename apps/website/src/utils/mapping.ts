/**
 * Remaps an object's values.
 *
 * @param input Object to map the keys of.
 * @param valueTransformer A function to transform the value.
 * @return The remapped object.
 */
export const remapObjectValues = <T extends Record<string, V>, V>(
	input: T,
	valueTransformer: (key: string, value: V) => V,
): T => {
	const remappedObject: Record<string, unknown> = {};

	for (const [key, value] of Object.entries(input)) {
		remappedObject[key] = valueTransformer(key, value);
	}

	return remappedObject as T;
};
