/**
 * This file contains all the used cache tags used within the application.
 */

/**
 * The arguments for {@link buildPayloadPageCacheTags}.
 */
interface BuildPayloadPageCacheTagArgs {
	/**
	 * The slug of the page.
	 */
	path: string;

	/**
	 * The locale of the page.
	 */
	locale?: string;
}

/**
 * Registry for the payload cache.
 */
export const payloadCacheTags = {
	all: "__payload__",
	lookup: "__payload_lookup__",
	pagePrefix: "__payload_page_",
	collectionMapping: "__payload_collection_mapping__",
} as const;

/**
 * Builds the cache tags for a page. The page is defined through its slug and locale.
 * @param args
 */
export const buildPayloadPageCacheTags = (
	args: BuildPayloadPageCacheTagArgs,
): string[] => [
	payloadCacheTags.all,
	`${payloadCacheTags.pagePrefix}|${args.path}`,
	// If the locale is available, then further scope down the cache tag.
	...(args.locale
		? [`${payloadCacheTags.pagePrefix}|${args.path}|${args.locale}`]
		: []),
];
