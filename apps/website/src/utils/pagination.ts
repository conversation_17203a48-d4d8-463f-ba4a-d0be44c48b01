interface PaginationOpts {
	currentPage: number;
	totalPages: number;
}

interface Pagination {
	current: number;
	total: number;
	previous: number | null;
	next: number | null;
	items: (number | null)[];
}

/**
 * Builds a pagination object for the given options.
 *
 * @param opts Current page and total pages.
 */
export const buildPagination = (opts: PaginationOpts): Pagination => {
	let prev = opts.currentPage === 1 ? null : opts.currentPage - 1;

	let next = opts.currentPage === opts.totalPages ? null : opts.currentPage + 1;

	let items: (number | null)[] = [1];

	if (opts.currentPage === 1 && opts.totalPages === 1) {
		return {
			current: opts.currentPage,
			total: opts.totalPages,
			previous: prev,
			next,
			items,
		};
	}

	if (opts.currentPage > 4) {
		items.push(null);
	}

	let r = 2;
	let r1 = opts.currentPage - r;
	let r2 = opts.currentPage + r;

	for (let i = r1 > 2 ? r1 : 2; i <= Math.min(opts.totalPages, r2); i++)
		items.push(i);

	if (r2 + 1 < opts.totalPages) {
		items.push(null);
	}

	if (r2 < opts.totalPages) {
		items.push(opts.totalPages);
	}

	return {
		current: opts.currentPage,
		total: opts.totalPages,
		previous: prev,
		next,
		items,
	};
};
