/**
 * Returns the value of an environment variable at runtime.
 * This is a nasty hack to trick Next.js into not replacing the value at build time.
 *
 * Warning: This function must not be used in client-side code.
 *
 * @param name The name of the environment variable.
 */
export function getRuntimeEnvVar(name: string) {
	if (!isRSC()) {
		throw new Error("getRuntimeEnvVar() must not be used in client-side code.");
	}

	return global.process.env[name];
}

/**
 * Will return if the current environment is a React Server Component.
 * This check is based on the presence of the `document` global variable.
 * If it is not present, we assume that we are in a React Server Component.
 *
 * @returns {boolean} True if the current environment is a React Server Component.
 */
export const isRSC = () => {
	return typeof document === "undefined";
};
