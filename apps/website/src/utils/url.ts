import { getRuntimeEnvVar } from "@/utils/env";

interface FormatUrlOpts {
	/**
	 * If true, the trailing slash will be removed from the URL.
	 */
	removeTrailingSlash?: boolean;
}

/**
 * Builds an absolute URL based on the given path.
 *
 * @param path The path to build the URL from.
 */
export const buildAbsoluteUrl = (path?: string): URL => {
	const baseUrl = getRuntimeEnvVar("APP_BASE_URL") ?? "http://localhost:3000";
	return new URL(path ?? "", baseUrl);
};

/**
 * Formats the given URL based on the given options. The output is always a string.
 *
 * @param url The URL to format.
 * @param opts The options to use for formatting.
 */
export const formatUrl = (url: URL, opts: FormatUrlOpts): string => {
	let asString = url.toString();

	// Apply the transformations.
	if (opts.removeTrailingSlash) {
		asString = asString.replace(/\/$/, "");
	}

	return asString;
};
