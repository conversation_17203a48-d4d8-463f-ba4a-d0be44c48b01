/**
 * Safely resolves a value at a given path within an object.
 * @param obj - The context object.
 * @param path - Dot-separated path string, e.g. "project.title".
 * @returns The value found or undefined if any part is missing.
 */
function getPath(obj: any, path: string): any {
	return path.split(".").reduce((acc, key) => {
		if (acc && typeof acc === "object" && key in acc) {
			return acc[key];
		}
		return undefined;
	}, obj);
}

/**
 * Extracts all placeholders of the form :field.path from the template, ignoring escaped colons (\\:).
 * @param template - The string containing placeholders.
 * @returns An array of unique field paths used in the template.
 */
export function analyzeStringTemplate(template: string): string[] {
	const regex = /\\:|:([a-zA-Z0-9_.]+)/g;
	const fields = new Set<string>();
	let match: RegExpExecArray | null;

	while ((match = regex.exec(template)) !== null) {
		const path = match[1];
		if (path) {
			fields.add(path);
		}
	}

	return Array.from(fields);
}

/**
 * Renders a template by substituting placeholders with context values.
 * Supports escaping a colon with \\: to render a literal ':'.
 * @param template - The string containing placeholders, e.g. "Project: :project.title" or "\\:notAField".
 * @param context - An object providing values for fields, e.g. { project: { title: "XYZ" } }.
 * @param missingPlaceholderHandler - Optional function to handle missing values. Defaults to leaving the placeholder as-is.
 * @returns The rendered string.
 */
export function renderStringTemplate(
	template: string,
	context: Record<string, any>,
	missingPlaceholderHandler?: (placeholder: string) => string,
): string {
	const handler = missingPlaceholderHandler || ((ph) => ph);
	const regex = /\\:|:([a-zA-Z0-9_.]+)/g;

	return template.replace(regex, (match, path) => {
		// Handle escaped colon
		if (match === "\\:") {
			return ":";
		}

		// Normal placeholder
		const value = getPath(context, path);
		if (value === undefined || value === null) {
			return handler(`:${path}`);
		}
		return String(value);
	});
}
