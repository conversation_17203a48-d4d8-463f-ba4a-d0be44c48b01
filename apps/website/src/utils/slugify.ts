/**
 * Creates a slug from the given input.
 *
 * @param input
 */
export const slugifyString = (input: string): string => {
	return (
		input
			.toLowerCase()
			// Replace German umlauts and their uppercase forms
			.replace(
				/[äöüÄÖÜ]/g,
				(c) => ({ ä: "ae", ö: "oe", ü: "ue", Ä: "ae", Ö: "oe", Ü: "ue" })[c]!,
			)
			// Replace ß with ss
			.replace(/ß/g, "ss")
			// Normalize superscript two
			.replace(/²/g, "2")
			// Remove thousand separators in numbers (e.g., 6.200 -> 6200)
			.replace(/(\d)\.(?=\d{3}(\D|$))/g, "$1")
			// Replace any sequence of non-alphanumeric characters with a dash
			.replace(/[^a-z0-9]+/g, "-")
			// Collapse multiple dashes
			.replace(/-+/g, "-")
			// Trim leading/trailing dashes
			.replace(/^-+|-+$/g, "")
	);
};
