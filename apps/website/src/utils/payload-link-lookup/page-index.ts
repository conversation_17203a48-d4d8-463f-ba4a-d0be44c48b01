import payloadConfig from "@payload-config";
import { getPayload } from "payload";

import { locales } from "@/i18n";

import type { PayloadDocumentSlugs } from "@/utils/payload-link-lookup/types";

interface CreatePayloadDocumentIndexArgs {
	isPreview?: boolean;
}

/**
 * Describes an entry in the page index.
 * This is basically a page with all the slugs in the available locales.
 */
export interface PayloadDocumentIndexEntry {
	pageId: string;
	slugs: PayloadDocumentSlugs;
}

/**
 * Creates the page index for payload.
 * This is used to resolve the slugs of a page.
 *
 * @returns The page index entries.
 */
export async function createPayloadDocumentIndex(
	args?: CreatePayloadDocumentIndexArgs,
): Promise<PayloadDocumentIndexEntry[]> {
	const payload = await getPayload({ config: payloadConfig });
	const isPreview = args?.isPreview ?? false;

	// Fetch all the pages.
	const pageDocs = (
		await payload.find({
			collection: "pages",
			locale: "all",
			depth: 0,
			limit: 1000,

			// If this is intended for preview mode, we include draft versions.
			draft: isPreview,

			// If we are not in preview mode, we only include published pages.
			...(!isPreview && {
				where: { _status: { equals: "published" } },
			}),
		})
	).docs;

	const projectDocs = (
		await payload.find({
			collection: "projects",
			locale: "all",
			depth: 0,
			limit: 1000,

			// If this is intended for preview mode, we include draft versions.
			draft: isPreview,

			// If we are not in preview mode, we only include published pages.
			...(!isPreview && {
				where: { _status: { equals: "published" } },
			}),
		})
	).docs;

	return [...pageDocs, ...projectDocs].map((page) => {
		// Create an object with all the slugs.
		// The type information of the response is kind of wrong here.
		// We load the pages with "locale: all" to get all locale variants.

		const allSlugs = Object.fromEntries(
			locales.map((locale) => [locale, (page.slug as any)[locale]]),
		);

		// Create an entry for the page.
		return {
			pageId: page.id,
			slugs: allSlugs as PayloadDocumentSlugs,
		};
	});
}
