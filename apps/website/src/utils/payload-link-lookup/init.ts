import { cacheTag } from "next/dist/server/use-cache/cache-tag";

import { contextExtension } from "./context";
import { createPayloadLinkLookupTable } from "./lookup";
import { payloadCacheTags } from "@/utils/cache";
import { createPayloadDocumentIndex } from "@/utils/payload-link-lookup/page-index";

import type { PayloadLinkLookup } from "./lookup";
import type { PayloadBlockContext } from "../payload-block";

type GetLookupContext = Pick<PayloadBlockContext, "isPreview">;

/**
 * Fetches the link lookup table.
 */
const _getLookup = async (
	ctx: GetLookupContext,
): Promise<PayloadLinkLookup> => {
	"use cache";
	cacheTag(payloadCacheTags.all, payloadCacheTags.lookup);

	const index = await createPayloadDocumentIndex({
		isPreview: ctx.isPreview,
	});

	return await createPayloadLinkLookupTable(index);
};

/**
 * Initializes the given context with the link lookup table.
 *
 * @param ctx The context to initialize.
 */
export const initPayloadLinkLookupFacade = async (
	ctx: PayloadBlockContext,
): Promise<void> => {
	contextExtension.set(
		ctx,
		await _getLookup({
			isPreview: ctx.isPreview,
		}),
	);
};
