import { contextExtension } from "./context";

import type { AvailableLocale } from "@/i18n";
import type { PayloadBlockContext } from "@/utils/payload-block";
import type { PayloadDocumentSlugs } from "@/utils/payload-link-lookup/types";

interface LookupPayloadDocumentSlugsOpts {
	ctx: PayloadBlockContext;
	pageId: string;
}

interface LookupPayloadDocumentIdOpts {
	ctx: PayloadBlockContext;
	slug: string;
	locale: AvailableLocale;
}

export const lookupPayloadDocumentSlugs = (
	opts: LookupPayloadDocumentSlugsOpts,
): PayloadDocumentSlugs | undefined => {
	const lookup = contextExtension.get(opts.ctx);

	if (!lookup) {
		return undefined;
	}

	return lookup[opts.pageId];
};

export const lookupPayloadDocumentId = (
	opts: LookupPayloadDocumentIdOpts,
): string | undefined => {
	const lookup = contextExtension.get(opts.ctx);

	if (!lookup) {
		return undefined;
	}

	return lookup[`${opts.locale}-${opts.slug}`];
};
