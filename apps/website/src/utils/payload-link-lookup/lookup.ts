import type { AvailableLocale } from "@/i18n";
import type { PayloadDocumentIndexEntry } from "@/utils/payload-link-lookup/page-index";
import type { PayloadDocumentSlugs } from "@/utils/payload-link-lookup/types";

/**
 * Pairs for the lookup table.
 */
export type PayloadLinkLookup = Record<string, PayloadDocumentSlugs> &
	Record<`${AvailableLocale}-${string}`, string>;

/**
 * Creates the lookup table for the payload links.
 *
 * @param entries
 */
export const createPayloadLinkLookupTable = async (
	entries: PayloadDocumentIndexEntry[],
): Promise<PayloadLinkLookup> => {
	const lookup: PayloadLinkLookup = {};

	for (let entry of entries) {
		// Page ID -> Slugs
		lookup[entry.pageId] = entry.slugs;

		// [Slug] -> Page ID
		for (let locale of Object.keys(entry.slugs)) {
			lookup[
				`${locale as AvailableLocale}-${entry.slugs[locale as AvailableLocale]}`
			] = entry.pageId;
		}
	}

	return lookup;
};
