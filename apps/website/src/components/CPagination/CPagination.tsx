import clsx from "clsx";
import { ChevronLeft } from "lucide-react";

import { <PERSON> } from "@/navigation";

import type { FC } from "react";

interface Props {
	currentPage: number;
	totalPages: number;

	nextPageLink?: string;
	prevPageLink?: string;
}

export const CPagination: FC<Props> = (props) => {
	return (
		<div className={clsx("flex justify-between items-center")}>
			<Link
				href={props.prevPageLink ?? ""}
				className={clsx(
					!props.prevPageLink &&
						"pointer-events-none text-black/60 disabled:cursor-not-allowed",
				)}
				scroll={false}
			>
				<ChevronLeft className="h-10 lg:h-14 w-auto" />
			</Link>

			<div className={clsx("text-body font-bold")}>
				{props.currentPage} / {props.totalPages}
			</div>

			<Link
				href={props.nextPageLink ?? ""}
				className={clsx(
					!props.nextPageLink &&
						"pointer-events-none text-black/60 disabled:cursor-not-allowed",
				)}
				scroll={false}
			>
				<ChevronLeft className="h-10 lg:h-14 w-auto rotate-180" />
			</Link>
		</div>
	);
};
