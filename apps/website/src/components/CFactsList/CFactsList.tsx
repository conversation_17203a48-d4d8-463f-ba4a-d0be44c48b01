import clsx from "clsx";

import type { FC, HTMLAttributes, ReactNode } from "react";

type CFactsListProps = HTMLAttributes<HTMLDivElement> & {
	children?: ReactNode;
};

interface CFactsListItemProps {
	title: ReactNode;
	children?: ReactNode;
}

/**
 * Displays a list of facts. Must use `CFactsListItem` as direct children.
 */
export const CFactsList: FC<CFactsListProps> = ({ children, ...rest }) => {
	return (
		<div
			{...rest}
			className={clsx(
				"grid grid-cols-[auto_1fr] gap-y-1 gap-x-8 text-body",
				rest.className,
			)}
		>
			{children}
		</div>
	);
};

export const CFactsListItem: FC<CFactsListItemProps> = ({
	title,
	children,
}) => {
	return (
		<>
			<div>{title}</div>
			<div className="font-bold line-clamp-2">{children}</div>
		</>
	);
};
