import "server-only";

import { createIndexBlockComponentsMapping } from "@/blocks/components-mapping";
import { validateBlockComponent } from "@/blocks/utils";
import { payloadBlockDataResolversExtension } from "@/utils/payload-block-data-resolver/context-extension";

import type { BaseBlock } from "@/blocks/utils/types";
import type { PayloadBlockContext } from "@/utils/payload-block";
import type { FC } from "react";

// Create an index of all blocks.
const indexedBlocks = createIndexBlockComponentsMapping();

interface Props {
	block: BaseBlock;
	ctx: PayloadBlockContext;
}

/**
 * Resolves the data for the given block. This accesses the context extension of the data resolvers.
 *
 * @param props
 */
const resolveData = (props: Props): any => {
	const instanceIdentity = `${props.block.blockType}$${props.block.id}`;
	const dataResolverResults = payloadBlockDataResolversExtension.get(props.ctx);

	return dataResolverResults?.[instanceIdentity];
};

/**
 * Checks if the given block can be rendered.
 *
 * @param props The props to check against.
 */
const canRender = (props: Props): boolean => {
	if (!props.block || !props.block.blockType) {
		console.error(`Invalid "block" props given`);
		return false;
	}

	const block = indexedBlocks[props.block.blockType];
	if (!block) {
		console.error(`Block of type "${props.block.blockType}" is not defined`);
		return false;
	}

	return validateBlockComponent(block, {
		...props,
		data: resolveData(props),
	});
};

/**
 * Functional component which takes care about rendering the individual blocks.
 * This must not run client-side.
 *
 * @param props
 * @constructor
 */
const FBlockRendererRoot: FC<Props> = (props) => {
	// Exit if the block can't be rendered.
	if (!canRender(props)) {
		return null;
	}

	const BlockComponent = indexedBlocks[props.block.blockType].component;

	return (
		<BlockComponent
			block={props.block}
			ctx={props.ctx}
			data={resolveData(props)}
		/>
	);
};

export const FBlockRenderer = Object.assign(FBlockRendererRoot, {
	canRender,
});
