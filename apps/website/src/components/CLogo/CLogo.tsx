import clsx from "clsx";
import { forwardRef } from "react";
import { tv } from "tailwind-variants";

import type { HTMLAttributes } from "react";
import type { VariantProps } from "tailwind-variants";

const variant = tv({
	variants: {
		color: {
			brand: "fill-[#003259]",
			white: "fill-white",
		},
	},
	defaultVariants: {
		color: "brand",
	},
});

type Props = HTMLAttributes<SVGSVGElement> & VariantProps<typeof variant>;

export const CLogo = forwardRef<SVGSVGElement, Props>((props, ref) => {
	const classNames = variant(props);

	return (
		<svg
			{...props}
			ref={ref}
			xmlns="http://www.w3.org/2000/svg"
			viewBox="0 0 567 123"
			className={clsx(props.className, classNames)}
		>
			<path
				fillRule="nonzero"
				d="m495.8 4.4.1 113.9h71V98.5h-48.2V72.1h35V52.3h-35l-.2-28.2h45.2V4.4h-67.9Zm-13.1 113.9-31.4-48.9c15.1-3.2 22.2-15.9 22.2-30.5s-6.1-21.2-16.3-27.4c-10.9-6.3-23.5-7.1-35.7-7.1h-29.6v113.9h25.2V24.1h8.7c10.2 0 21.8 2.4 21.8 14.8s-12.1 14.4-22.5 14.4v18.1c12.4 15.6 30.3 46.8 30.3 46.8h27.3v.1Zm-105.5-57C377.2 27.5 349.8.1 316 .1c-33.8 0-61.2 27.4-61.2 61.2 0 33.8 27.4 61.2 61.2 61.2 33.8 0 61.2-27.4 61.2-61.2m-22.9 0c0 21.2-17.2 38.4-38.4 38.4s-38.4-17.2-38.4-38.4 17.2-38.4 38.4-38.4 38.4 17.2 38.4 38.4m-180.5.7c0-20 13.9-38.3 36.9-38.3s18.3 3.9 24.7 9.5c5.2-6.5 8.8-11.2 14.7-18.8C239.4 5.4 225.7 0 210.7 0c-33.8 0-61.2 27.4-61.2 61.2 0 33.8 27.4 61.2 61.2 61.2 33.8 0 28.8-5.4 39.4-14.4-5.3-6.7-9.1-11.5-14.7-18.6-6.4 5.6-15.6 9.5-24.7 9.3-23.5-.3-36.9-17.1-36.9-37m-68.3 56.6h25.3V4.4h-25.4v113.9h.1ZM79.3 6.2C69 1.5 55.8 0 46.1.1 26 .1.7 7.3.7 31.8s10.2 26.3 24.5 32.8c5.3 2.5 10.5 4.4 15.8 6.5 7.2 3 19.8 7.7 19.8 17.2S47.8 102 38.9 102C25.2 102 11.6 96.4 0 89.6v24.7c12.1 5.8 25.4 8.4 38.9 8.4 22.4 0 47.7-8.9 47.7-35.4S73.8 58.9 56.8 51.2c-6-2.6-11.9-4.9-17.7-7.7-4.9-2.3-12.8-6-12.8-12.6s11.6-10.3 18.4-10.4c10.3 0 23.8 2.8 34.2 7.5l.4-21.7v-.1Z"
			/>
		</svg>
	);
});
