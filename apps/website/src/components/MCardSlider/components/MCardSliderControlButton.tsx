import clsx from "clsx";

import type { FC, ReactNode } from "react";

interface Props {
	children: ReactNode;
	onClick?: () => void;
}

export const MCardSliderControlButton: FC<Props> = ({ children, onClick }) => {
	return (
		<button
			className={clsx(
				"h-full cursor-pointer",
				"text-black disabled:text-black/60 disabled:cursor-not-allowed",
				"transition-config-default transition-colors",
			)}
			disabled={!onClick}
			onClick={() => onClick?.()}
		>
			{children}
		</button>
	);
};
