"use client";
import clsx from "clsx";
import "keen-slider/keen-slider.min.css";
import { useKeenSlider } from "keen-slider/react";
import { ChevronLeft } from "lucide-react";
import { type FC, type PropsWithChildren, useMemo, useState } from "react";

import { MCardSliderControlButton } from "@/components/MCardSlider/components/MCardSliderControlButton";

type MCardSliderProps = PropsWithChildren & {
	singlePerView?: boolean;

	/**
	 * Callback when the slide changes.
	 */
	onSlideChange?: (slide: number) => void;

	/**
	 * Initial slide to show.
	 */
	initialSlide?: number;
};

type MCardSliderPersistedProps = Omit<MCardSliderProps, "initialSlide"> & {
	/**
	 * Unique key to persist the slider state.
	 */
	persistenceKey: string;
};

export const MCardSlider: FC<MCardSliderProps> = (props) => {
	// Default to the first slide. Starts at 1, *NOT 0*.
	const initialSlide = props.initialSlide ?? 1;

	const [currentSlide, setCurrentSlide] = useState<number | undefined>(
		undefined,
	);

	const [sliderRef, instanceRef] = useKeenSlider({
		initial: props.initialSlide,
		slideChanged: (it) => {
			setCurrentSlide(it.track.details.rel);
			props.onSlideChange?.(it.track.details.rel);
		},
		created: () => setCurrentSlide(initialSlide),
		slides: {
			perView: 1,
			spacing: 32,
		},
		breakpoints: {
			...(!(props.singlePerView ?? false) && {
				"(min-width: 640px)": {
					slides: {
						perView: 2,
						spacing: 64,
					},
				},
				"(min-width: 1024px)": {
					slides: {
						perView: 3,
						spacing: 64,
					},
				},
			}),
		},
	});

	const config = useMemo(() => {
		const slidesPerView = (instanceRef.current?.options.slides as any)?.perView;
		const slidesTotal = instanceRef.current?.track.details.slides.length ?? 0;
		const _currentSlide = currentSlide ?? 0;

		const canPrev = _currentSlide > 0;
		const canNext = _currentSlide + (slidesPerView - 1) < slidesTotal - 1;

		return {
			canPrev,
			canNext,
			slidesPerView,
			slidesTotal,
		};
	}, [instanceRef, currentSlide]);

	return (
		<div className="flex">
			<div className="px-4">
				<MCardSliderControlButton
					onClick={config.canPrev ? instanceRef.current?.prev : undefined}
				>
					<ChevronLeft className="h-14 w-14" />
				</MCardSliderControlButton>
			</div>

			<div className={clsx("keen-slider")} ref={sliderRef}>
				{Array.isArray(props.children) &&
					props.children.map((it, idx) => (
						<div key={idx} className="keen-slider__slide">
							{it}
						</div>
					))}
			</div>

			<div className="px-4">
				<MCardSliderControlButton
					onClick={config.canNext ? instanceRef.current?.next : undefined}
				>
					<ChevronLeft className="h-14 w-14 rotate-180" />
				</MCardSliderControlButton>
			</div>
		</div>
	);
};

/**
 * Adapter for the {@link MCardSlider} that persists the slider state in the browser history.
 * This is necessary to keep the slider state when navigating back and forth between pages.
 *
 * @param props
 * @constructor
 */
export const MCardSliderPersisted: FC<MCardSliderPersistedProps> = (props) => {
	const stateKey = `${props.persistenceKey}-slide`;

	// Resolve the initial slide from the browser history.
	let initialSlide;
	if (typeof window !== "undefined") {
		initialSlide = window?.history?.state?.[stateKey];
	}

	return (
		<MCardSlider
			{...props}
			initialSlide={initialSlide}
			onSlideChange={(slide) => {
				props.onSlideChange?.(slide);
				window.history.replaceState(
					{ ...window.history.state, [stateKey]: slide },
					"",
				);
			}}
		/>
	);
};
