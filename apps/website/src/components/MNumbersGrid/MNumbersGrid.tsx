"use client";
import clsx from "clsx";
import * as motion from "motion/react-client";
import CountUp from "react-countup";
import { useIsClient } from "usehooks-ts";

import type { FC, ReactNode } from "react";

interface RootProps {
	children: ReactNode;
	perRow: number;
}

interface ItemProps {
	number: number;
	decimals?: number;

	title: string;
	description: string;
}

export const MNumbersGrid: FC<RootProps> = (props) => {
	// Normalize the per-row prop to be in a range of 3-4
	const perRow = Math.max(3, Math.min(4, props.perRow));

	return (
		<motion.div
			className={clsx(
				"grid grid-cols-1",
				{
					"sm:grid-cols-2 lg:grid-cols-3": perRow === 3,
					"sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4": perRow === 4,
				},
				"lg:gap-x-38 lg:gap-y-18",
				"gap-x-28 gap-y-12",
			)}
			viewport={{ once: true }}
			initial="hidden"
			whileInView="visible"
			variants={{
				visible: {
					opacity: 1,
					transition: {
						when: "beforeChildren",
						staggerChildren: 0.25,
					},
				},
			}}
		>
			{props.children}
		</motion.div>
	);
};

export const MNumbersGridItem: FC<ItemProps> = (props) => {
	const isClient = useIsClient();

	return (
		<motion.div
			className="flex flex-col items-center justify-center gap-y-4 h-48 lg:h-52 xl:h-52"
			variants={{
				hidden: { opacity: 0, y: 20, filter: "blur(3px)" },
				visible: {
					opacity: 1,
					y: 0,
					filter: "blur(0px)",
					transition: { duration: 0.5 },
				},
			}}
		>
			<div className="flex-1 flex flex-col justify-end text-center">
				<div className="text-body-smaller font-display text-xl uppercase text-center line-clamp-2">
					{props.title}
				</div>
			</div>
			<div className="flex-1 font-display text-5xl lg:text-6xl xl:text-7xl font-bold">
				{isClient ? (
					<CountUp
						enableScrollSpy={true}
						scrollSpyOnce={true}
						end={props.number}
						decimals={props.decimals ?? 0}
						duration={2.5}
						decimal=","
					/>
				) : (
					"0"
				)}
			</div>
			<div className="flex-1 flex flex-col justify-start text-center">
				<div className="text-body-smaller font-bold line-clamp-2">
					{props.description}
				</div>
			</div>
		</motion.div>
	);
};
