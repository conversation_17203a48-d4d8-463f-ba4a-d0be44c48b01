import { PayloadLexicalReact } from "@zapal/payload-lexical-react";

import type { PayloadLexicalReactContent } from "@zapal/payload-lexical-react";
import type { SerializedLexicalNode } from "lexical";
import type { FC } from "react";

interface Props {
	content: PayloadLexicalReactContent;

	/**
	 * If true, only the first paragraph will be rendered.
	 * This will also strip the paragraph node and provide the children as the root nodes.
	 */
	firstParagraphOnly?: boolean;
}

export const FLexicalRenderer: FC<Props> = ({
	content,
	firstParagraphOnly,
}) => {
	let contentToRender: PayloadLexicalReactContent = content;

	// Modify the content if only the first paragraph should be rendered.
	if (firstParagraphOnly) {
		const children: SerializedLexicalNode[] = [];

		// Find the first paragraph node.
		const paragraph = content?.root.children.find(
			(it) => it.type === "paragraph",
		);

		// The paragraph node must contain children.
		// We then take all the children and add them to the children array.
		if (paragraph && paragraph.type === "paragraph") {
			children.push(...(paragraph as any).children);
		}

		contentToRender = {
			root: {
				...content?.root,
				children,
			},
		};
	}

	return (
		<PayloadLexicalReact
			content={contentToRender}
			// eslint-disable-next-line react/no-unstable-nested-components
			mark={(mark) => {
				if (mark.bold) {
					return <b>{mark.text}</b>;
				}

				if (mark.italic) {
					return <i>{mark.text}</i>;
				}

				if (mark.subscript) {
					return <sub>{mark.text}</sub>;
				}

				if (mark.superscript) {
					return <sup>{mark.text}</sup>;
				}

				return mark.text;
			}}
		/>
	);
};
