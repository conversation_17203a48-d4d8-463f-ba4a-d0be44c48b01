"use client";
import clsx from "clsx";
import { useState } from "react";

import type { FC, ReactNode } from "react";

interface Props {
	image: ReactNode;
	title: ReactNode;
	body: ReactNode;
}

export const CSquareCardValue: FC<Props> = (props) => {
	const [isClicked, setIsClicked] = useState(false);

	return (
		<div
			className={clsx("group cursor-pointer")}
			onClick={() => setIsClicked(!isClicked)}
		>
			<div className={clsx("absolute inset-0")}>{props.image}</div>

			<div
				className={clsx(
					"absolute inset-0 opacity-0 pointer-events-none p-4",
					"group-hover:opacity-100 group-hover:pointer-events-auto transition-opacity transition-config-default",
					"bg-highlighted",
					"text-white",
				)}
			>
				<h3 className="font-display uppercase text-2xl mb-4 text-balance hyphens-auto">
					{props.title}
				</h3>
			</div>

			<div
				className={clsx(
					"absolute inset-0 opacity-0 pointer-events-none p-4",
					"bg-black text-white",
					"text-xl",
					"transition-opacity transition-config-default",
					isClicked && "opacity-100 pointer-events-auto",
				)}
			>
				{props.body}
			</div>
		</div>
	);
};
