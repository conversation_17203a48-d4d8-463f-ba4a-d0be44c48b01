import clsx from "clsx";
import * as motion from "motion/react-client";
import { Children } from "react";

import type { FC, ReactNode } from "react";

interface Props {
	children: ReactNode;
}

export const MSquareCardGrid: FC<Props> = (props) => {
	return (
		<motion.div
			className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-12"
			viewport={{ once: true }}
			initial="hidden"
			whileInView="visible"
			variants={{
				visible: {
					opacity: 1,
					transition: {
						when: "beforeChildren",
						staggerChildren: 0.15,
					},
				},
			}}
		>
			{Children.map(props.children, (children, idx) => (
				<motion.div
					key={idx}
					variants={{
						hidden: {
							opacity: 0,
							y: 20,
							filter: "blur(3px)",
						},
						visible: {
							opacity: 1,
							y: 0,
							filter: "blur(0px)",
							transition: {
								duration: 0.5,
							},
						},
					}}
					className={clsx("aspect-square relative group")}
				>
					{children}
				</motion.div>
			))}
		</motion.div>
	);
};
