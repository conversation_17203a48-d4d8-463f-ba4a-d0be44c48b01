import { Slot } from "@radix-ui/react-slot";
import clsx from "clsx";

import { CLogo } from "@/components/CLogo";
import { CSectionSeparator } from "@/components/CSectionSeparator";
import { LContainer } from "@/components/LContainer";

import type { FC, ReactNode } from "react";

interface RootProps {
	children: ReactNode;
	details: ReactNode;
	bottomLine?: ReactNode;
}

interface GroupProps {
	title?: string;
	children: ReactNode;
}

interface ItemProps {
	children: ReactNode;
}

interface BottomLineProps {
	title: string;
	children: ReactNode;
}

interface BottomLineItemProps {
	children: ReactNode;
}

export const MFooter: FC<RootProps> = (props) => {
	return (
		<footer>
			<LContainer>
				<div className="bg-highlighted py-16 px-12 my-20 flex flex-col gap-y-12">
					<div className="">
						<CLogo color="white" className="h-10" />
					</div>

					<div className="flex flex-col lg:flex-row font-display gap-y-12">
						<div className="basis-2/5 text-black text-lg prose">
							{props.details}
						</div>

						<div className="basis-3/5 flex flex-col md:flex-row w-full gap-12">
							{props.children}
						</div>
					</div>
				</div>

				{props.bottomLine}
			</LContainer>
		</footer>
	);
};

export const MFooterGroup: FC<GroupProps> = (props) => {
	return (
		<div className="flex-1 text-white">
			<div className="h-12">
				{props.title && (
					<p className="font-display font-bold text-xl uppercase">
						{props.title}
					</p>
				)}
			</div>

			<div className="flex flex-col gap-y-3">{props.children}</div>
		</div>
	);
};

export const MFooterItem: FC<ItemProps> = (props) => {
	return (
		<Slot {...props} className={clsx("text-lg leading-tight uppercase")} />
	);
};

export const MFooterBottomLine: FC<BottomLineProps> = (props) => {
	return (
		<>
			<CSectionSeparator />

			<div className="flex flex-col md:flex-row gap-x-8 gap-y-4 py-8 items-end md:items-center justify-end">
				<p className="font-display text-xl uppercase">{props.title}</p>
				<div className="flex flex-row gap-x-4">{props.children}</div>
			</div>
		</>
	);
};

export const MFooterBottomLineItem: FC<BottomLineItemProps> = ({
	...props
}) => {
	return <Slot {...props} />;
};
