import { forwardRef } from "react";

import type { HTMLAttributes } from "react";

type Props = HTMLAttributes<SVGSVGElement> & {
	type: SocialIconType;
};

export type SocialIconType = "linkedin" | "xing";

export const CSocialIcon = forwardRef<SVGSVGElement, Props>((props, ref) => {
	const { type, ...rest } = props;

	if (type === "linkedin") {
		return (
			<svg
				xmlns="http://www.w3.org/2000/svg"
				viewBox="0 0 256 256"
				{...rest}
				ref={ref}
			>
				<g fill="none" fillRule="evenodd">
					<path fill="#000" d="M0 0h256v256H0z" />
					<path
						fill="#FFF"
						fillRule="nonzero"
						d="M206 199h-31.228916v-54.572094c0-14.970485-6.612723-23.321208-18.471903-23.321208-12.90535 0-20.564241 8.701594-20.564241 23.321208V199h-31.228916V97.6373441h31.228916v11.3993999s9.798072-17.1692741 31.876915-17.1692741C189.698506 91.8674699 206 105.325312 206 133.176651V199ZM69.2690029 81.5301205C58.6244964 81.5301205 50 72.9017537 50 62.2611454 50 51.6283667 58.6244964 43 69.2690029 43c10.6366158 0 19.2611176 8.6283667 19.2611176 19.2611454.0078852 10.6406083-8.6245018 19.2689751-19.2611176 19.2689751ZM50 199h39.4698795V97.5060241H50V199Z"
					/>
				</g>
			</svg>
		);
	} else if (type === "xing") {
		return (
			<svg
				xmlns="http://www.w3.org/2000/svg"
				viewBox="0 0 256 256"
				{...rest}
				ref={ref}
			>
				<g fill="none" fillRule="evenodd">
					<path fill="#000" d="M0 0h256v256H0z" />
					<g fill="#FFF" fillRule="nonzero">
						<path d="M80.5876976 164.594595c2.8161593 0 5.2283084-1.705698 7.2303207-5.123226 18.3159274-32.5135383 27.843695-49.4376283 28.5873871-50.7784033L98.1643577 76.8489631c-1.9288343-3.340523-4.3763811-5.0111253-7.3405982-5.0111253H64.2380443c-1.7082794 0-2.8913795.5567539-3.5598514 1.6702616-.81652969 1.1135076-.77908981 2.4876742.1106178 4.118411l17.9074924 31.0664553c.0728376.0790495.0728376.1127818 0 .1127818L50.55445052 158.693481c-.73926736 1.413691-.73926736 2.748333 0 4.00597.73960772 1.265814 1.89139444 1.895144 3.44753181 1.895144H80.5876976ZM201.942806 36H175.15796c-2.889069 0-5.26188 1.66324103-7.111972 4.9900624-38.0115723 67.2703298-57.684786 102.0911736-59.018961 104.4523516l37.6765832 68.973268c1.7776529 3.326482 4.2232438 4.989723 7.3347318 4.989723h26.562426c1.629373 0 2.815949-.554413 3.557006-1.663241.738677-1.260493.703307-2.628884-.110529-4.105172l-37.3415942-68.194578v-.107897L205.389623 41.76637726c.813836-1.55093324.813836-2.91525233 0-4.10110044C204.721345 36.55441368 203.572519 36 201.942806 36Z" />
					</g>
				</g>
			</svg>
		);
	}

	return null;
});
