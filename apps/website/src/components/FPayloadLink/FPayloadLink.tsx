"use client";
import { forwardRef } from "react";

import { <PERSON> } from "@/navigation";
import { resolvePayloadLink } from "@/utils/payload-block-utils";

import type { <PERSON>Field } from "@/gen/payload/types";
import type { PayloadBlockContext } from "@/utils/payload-block";
import type { ComponentPropsWithoutRef } from "react";

type Props = Omit<
	ComponentPropsWithoutRef<typeof Link>,
	keyof ReturnType<typeof resolvePayloadLink>
> & {
	ctx: PayloadBlockContext;
	link: LinkField | undefined;

	/**
	 * If true, the label of the link will be used as the children of the link.
	 * Otherwise, the children prop will be used.
	 */
	useLabel?: boolean;
};

/**
 * Functional component to render a Payload-defined link.
 * NOTE: This will always render a link, even if the link is not available.
 * You may check the presence of the link with the `isAvailablePayloadLink`-function.
 *
 * Why is this a "use client"-component? The link might use 'onClick'-handlers instead of href-attributes.
 */
export const FPayloadLink = forwardRef<HTMLAnchorElement, Props>(
	({ link, ctx, useLabel, ...props }, ref) => (
		<Link ref={ref} {...props} {...resolvePayloadLink({ link, ctx })}>
			{useLabel ? link?.label : props.children}
		</Link>
	),
);
