import clsx from "clsx";
import Image from "next/image";

import imgGradientDefault from "@/assets/images/gradient-default.jpg";
import { LContainer } from "@/components/LContainer";

import type { ModuleBlockBackground } from "@/gen/payload/types";
import type { ComponentPropsWithoutRef, FC, ReactNode } from "react";

interface PropsBlock {
	background?: ModuleBlockBackground;
}

type Props = ComponentPropsWithoutRef<typeof LContainer> & {
	children?: ReactNode;

	block: PropsBlock;
};

export const LModuleWrapper: FC<Props> = ({
	children,
	block,
	className,
	...props
}) => {
	return (
		<LContainer {...props}>
			<div
				className={clsx([
					"relative isolate overflow-hidden",
					block?.background?.type === "gradient" && "px-8 py-16",
					className,
				])}
			>
				{block?.background?.type === "gradient" && (
					<div className="absolute inset-0 overflow-hidden pointer">
						<Image
							src={imgGradientDefault}
							alt=""
							fill={true}
							priority={true}
						/>
					</div>
				)}

				{children}
			</div>
		</LContainer>
	);
};
