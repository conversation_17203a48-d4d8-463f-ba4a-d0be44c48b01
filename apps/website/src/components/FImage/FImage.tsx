import Image from "next/image";
import { forwardRef } from "react";

import type { Media } from "@/gen/payload/types";
import type { ImageProps } from "next/image";

type Props = Omit<ImageProps, "src" | "alt"> & {
	media: Media;
};

export const FImage = forwardRef<HTMLImageElement, Props>(
	({ media, ...props }, ref) => {
		// We can't render the image if no media is present.
		if (!media || !media.url) {
			return null;
		}

		return (
			<Image
				{...props}
				ref={ref}
				width={props.width ?? media.width!}
				height={props.height ?? media.height!}
				alt={media.alt ?? ""}
				placeholder="blur"
				blurDataURL={media.blurhash}
				src={media.url}
				unoptimized={media.mimeType === "image/svg+xml"}
				style={{
					objectFit: "cover",
					objectPosition: `${media.focalX ?? 50}% ${media.focalY ?? 50}%`,
					...(props.style ?? {}),
				}}
			/>
		);
	},
);
