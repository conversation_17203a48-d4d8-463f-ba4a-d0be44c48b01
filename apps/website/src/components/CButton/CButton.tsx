import clsx from "clsx";
import { forwardRef } from "react";
import { tv } from "tailwind-variants";

import { Link } from "@/navigation";

import type {
	ReactNode,
	ComponentPropsWithoutRef,
	HTMLAttributes,
} from "react";
import type { VariantProps } from "tailwind-variants";

const variant = tv({
	slots: {
		base: clsx(
			"inline-flex items-center justify-center gap-2 max-w-full",
			"font-display font-bold uppercase tracking-wider text-sm",
			"transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
		),
		inner: "",
	},
	variants: {
		variant: {
			default: {
				base: "bg-black text-white shadow hover:bg-primary/90",
			},
		},
		size: {
			default: {
				base: "px-4 py-2",
			},
			extended: {
				base: "px-6 py-2",
			},
		},
		padding: {
			default: {
				base: "",
			},
			extended: {
				base: "px-18",
			},
		},
		allowMultiLine: {
			true: {
				inner: "block min-w-0 line-clamp-2 break-words",
			},
			false: {
				inner: "block min-w-0 truncate",
			},
		},
	},
	defaultVariants: {
		variant: "default",
		size: "default",
		allowMultiLine: false,
	},
});

type BaseProps = VariantProps<typeof variant> & {
	children: ReactNode;
};

type ButtonProps = HTMLAttributes<HTMLButtonElement> & BaseProps;
type LinkButtonProps = ComponentPropsWithoutRef<typeof Link> & BaseProps;

export const CButton = forwardRef<HTMLButtonElement, ButtonProps>(
	({ children, ...props }, ref) => {
		const classNames = variant(props);

		return (
			<button className={classNames.base()} {...props} ref={ref}>
				<span className={classNames.inner()}>{children}</span>
			</button>
		);
	},
);

export const CButtonLink = forwardRef<HTMLAnchorElement, LinkButtonProps>(
	({ ...props }, ref) => {
		const classNames = variant(props);
		return (
			<Link
				{...props}
				ref={ref}
				className={clsx(classNames.base(), props.className)}
			>
				<span className={classNames.inner()}>{props.children}</span>
			</Link>
		);
	},
);
