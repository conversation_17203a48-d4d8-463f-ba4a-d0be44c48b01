import clsx from "clsx";
import { forwardRef } from "react";
import { tv } from "tailwind-variants";

import type { HTMLAttributes, ReactNode } from "react";
import type { VariantProps } from "tailwind-variants";

const variant = tv({
	base: "mx-auto px-6 lg:px-8",
	variants: {
		size: {
			default: "max-w-8xl",
			small: "max-w-6xl",
		},
	},
	defaultVariants: {
		size: "default",
	},
});

type Props = HTMLAttributes<HTMLDivElement> & {
	children?: ReactNode;
} & VariantProps<typeof variant>;

export const LContainer = forwardRef<HTMLDivElement, Props>((props, ref) => {
	const resolved = variant(props);

	return (
		<div {...props} ref={ref} className={clsx(resolved, props.className)}>
			{props.children}
		</div>
	);
});
