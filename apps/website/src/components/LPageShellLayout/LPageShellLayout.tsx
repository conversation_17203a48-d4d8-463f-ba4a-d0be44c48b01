import clsx from "clsx";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/FBlockRenderer";
import { LPageWrapper } from "@/components/LPageWrapper/LPageWrapper";
import {
	createChildrenPayloadBlockContext,
	isFirstSectionExtension,
} from "@/utils/payload-block";

import type { PageShellLayout } from "@/gen/payload/types";
import type { PayloadBlockContext } from "@/utils/payload-block";
import type { FC } from "react";

interface LPageShellLayoutProps {
	data: PageShellLayout;
	ctx: PayloadBlockContext;
}

export const LPageShellLayout: FC<LPageShellLayoutProps> = ({ data, ctx }) => {
	return (
		<LPageWrapper
			className={clsx([
				data.color === "meadow" && `[--color-highlighted:var(--color-meadow)]`,
				data.color === "sky" && `[--color-highlighted:var(--color-sky)]`,
			])}
		>
			<FBlockRenderer block={data.header[0]} ctx={ctx} />

			<main className="relative">
				{data.sections.map((section, idx) => {
					let _ctx = ctx;

					// If this is the first section, we add an extension to the context.
					if (idx === 0) {
						// Create a new children context to not mutate the root and all the siblings.
						_ctx = createChildrenPayloadBlockContext(_ctx);

						isFirstSectionExtension.set(_ctx, true);
					}

					return <FBlockRenderer block={section} ctx={_ctx} key={idx} />;
				})}
			</main>

			<FBlockRenderer block={data.footer[0]} ctx={ctx} />

			{data.floating && data.floating.length > 0 && (
				<div>
					{data.floating.map((floating, idx) => (
						<FBlockRenderer block={floating} ctx={ctx} key={idx} />
					))}
				</div>
			)}
		</LPageWrapper>
	);
};
