"use client";
import { useRowLabel } from "@payloadcms/ui";

import type { FC } from "react";

interface Props {
	field?: string;
	childrenField?: string;
}

export const CustomRowLabel: FC<Props> = ({ field, childrenField }) => {
	const { data, rowNumber } = useRowLabel<any>();

	let label = String(rowNumber).padStart(2, "0");

	if (field) {
		label += `, ${data?.[field ?? ""] ?? "?"}`;
	}

	if (childrenField) {
		label += `, ${data?.[childrenField ?? ""]?.length ?? 0} children`;
	}

	return label;
};
