"use client";
import { Button, toast, useConfig, useTranslation } from "@payloadcms/ui";

import type { FC } from "react";

export const JobsSyncAction: FC = () => {
	const { config } = useConfig();
	const { t } = useTranslation();

	const submitJobsSync = async () => {
		toast.promise(
			fetch(`${config.serverURL}${config.routes.api}/jobs/sync`, {
				method: "POST",
			}),
			{
				loading: t("general:loading"),
				success: "Sync task scheduled",
			},
		);
	};

	return (
		<Button size="small" onClick={() => submitJobsSync()}>
			Sync Jobs
		</Button>
	);
};
