"use client";
import {
	FieldLabel,
	TextInput,
	useAllFormFields,
	useField,
} from "@payloadcms/ui";

import { slugifyString } from "@/utils/slugify";

import type { TextFieldClientProps } from "payload";
import type { FC } from "react";

export type SlugInputProps = TextFieldClientProps & {
	trackingFields: string | string[];
};

export const SlugInput: FC<SlugInputProps> = (props) => {
	const trackingFields = Array.isArray(props.trackingFields)
		? props.trackingFields
		: [props.trackingFields];

	const slugFieldTarget = useField<string>({
		path: props.path,
	});

	// Get all fields from the form.
	const [allFields] = useAllFormFields();

	// Generates the slug from the tracking fields.
	const generateSlug = (): string => {
		let output = trackingFields
			.map((field) => {
				const trackingField = allFields[field];
				if (!trackingField) {
					return "";
				}

				return trackingField.value;
			})
			.join(" ")
			.toLowerCase();

		// Slugify the output.
		return slugifyString(output);
	};

	return (
		<TextInput
			description={props.field.admin?.description}
			localized={props.field.localized}
			path={props.path}
			value={slugFieldTarget.value}
			onChange={slugFieldTarget.setValue}
			Label={
				<div className="tw:flex tw:[&>.field-label]:!mr-0">
					<FieldLabel {...props.field} path={props.path} />
					<span>&nbsp; &mdash; &nbsp;</span>
					<span
						className="tw:text-current tw:mb-[5px] tw:text-[13px] tw:leading-[20px] tw:underline tw:cursor-pointer"
						onClick={(e) => {
							e.preventDefault();
							slugFieldTarget.setValue(generateSlug());
						}}
					>
						Generate
					</span>
				</div>
			}
		/>
	);
};
