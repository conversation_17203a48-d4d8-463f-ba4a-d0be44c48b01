import { mongoose<PERSON><PERSON>pter } from "@payloadcms/db-mongodb";
import { redirectsPlugin } from "@payloadcms/plugin-redirects";
import { lexicalEditor } from "@payloadcms/richtext-lexical";
import { s3Storage } from "@payloadcms/storage-s3";
import { de } from "@payloadcms/translations/languages/de";
import { en } from "@payloadcms/translations/languages/en";
import path from "path";
import { buildConfig } from "payload";
import sharp from "sharp";
import { fileURLToPath } from "url";

import { syncJobsHandler } from "@/jobs/tasks/sync-jobs-handler";
import { allCollections, pagesCollection } from "@/payload/collections";
import { usersCollection } from "@/payload/collections/users-collection";
import { allGlobals } from "@/payload/globals";
import { buildAbsoluteUrl } from "@/utils/url";

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

export default buildConfig({
	admin: {
		user: usersCollection.slug,
		importMap: {
			baseDir: path.resolve(dirname),
		},
		livePreview: {
			url: ({ data, collectionConfig, locale }) => {
				if (collectionConfig?.slug === pagesCollection.slug) {
					return buildAbsoluteUrl(
						`/${locale.code}/-preview${data.slug}`,
					).toString();
				}

				return "/";
			},
			collections: [pagesCollection.slug],
		},
		suppressHydrationWarning: true,
		meta: {
			titleSuffix: " - SICORE Real Assets GmbH",
			icons: [{ rel: "icon", type: "image/x-icon", url: "/favicon.ico" }],
		},
		components: {
			graphics: {
				Logo: "@/payload/admin-components/Logo#Logo",
			},
		},
	},
	// I18n configuration for the Admin UI.
	// Support English and German.
	i18n: {
		fallbackLanguage: "en",
		supportedLanguages: { de, en },
	},
	// Localization configuration for the data.
	localization: {
		defaultLocale: "de",
		locales: [
			{ code: "de", label: "German" },
			{ code: "en", label: "English" },
		],
	},
	jobs: {
		tasks: [
			{
				retries: 1,
				slug: "sync-jobs",
				handler: syncJobsHandler,
			},
		],
	},
	collections: [...allCollections],
	globals: [...allGlobals],
	editor: lexicalEditor(),
	secret: process.env.PAYLOAD_SECRET || "",
	typescript: {
		outputFile: path.resolve(dirname, "../../generated/payload", "types.ts"),
	},
	db: mongooseAdapter({
		url: process.env.DATABASE_URI ?? "",
		disableIndexHints: true,
		connectOptions: {
			autoIndex: false,
		},
	}),
	sharp,
	plugins: [
		s3Storage({
			collections: {
				media: true,
			},
			bucket: process.env.S3_BUCKET ?? "",
			config: {
				credentials: {
					accessKeyId: process.env.S3_ACCESS_KEY_ID ?? "",
					secretAccessKey: process.env.S3_SECRET_ACCESS_KEY ?? "",
				},
				region: process.env.S3_REGION ?? "",
				endpoint: process.env.S3_ENDPOINT ?? undefined,
				forcePathStyle: true,
				requestHandler: {
					httpAgent: {
						maxSockets: 500,
						keepAlive: true,
					},
					httpsAgent: {
						maxSockets: 500,
						keepAlive: true,
					},
					connectionTimeout: 5 * 1000,
					requestTimeout: 5 * 1000,
				},
			},
		}),
		redirectsPlugin({
			collections: [pagesCollection.slug],
		}),
	],
});
