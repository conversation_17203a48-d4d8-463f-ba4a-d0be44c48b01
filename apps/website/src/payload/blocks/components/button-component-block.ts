import { linkField } from "@/payload/fields/link-field";

import type { Block } from "payload";

export const buttonComponentBlock: Block = {
	slug: "button-component",
	interfaceName: "ButtonComponentBlock",
	fields: [
		{
			name: "label",
			type: "text",
			required: true,
			localized: true,
			admin: {
				description: `The label of the button.`,
			},
		},
		linkField({ required: true }),
	],
};
