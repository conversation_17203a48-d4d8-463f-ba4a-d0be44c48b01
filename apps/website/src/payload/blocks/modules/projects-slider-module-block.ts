import { createModuleBlockFields } from "@/payload/fields/module-block-fields";

import type { Block } from "payload";

export const projectsSliderModuleBlock: Block = {
	slug: "projects-slider-module",
	interfaceName: "ProjectsSliderModuleBlock",
	fields: createModuleBlockFields([
		{
			type: "relationship",
			name: "projects",
			relationTo: "projects",
			required: true,
			hasMany: true,
			admin: {
				description: `The projects to display in the slider.`,
			},
		},
	]),
};
