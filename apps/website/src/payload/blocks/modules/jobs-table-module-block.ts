import { createModuleBlockFields } from "@/payload/fields/module-block-fields";

import type { Block } from "payload";

export const jobsTableModuleBlock: Block = {
	slug: "jobs-table-module",
	interfaceName: "JobsTableModuleBlock",
	fields: createModuleBlockFields([
		{
			name: "labelTitle",
			type: "text",
			required: true,
			localized: true,
		},
		{
			name: "labelEntryLevel",
			type: "text",
			required: true,
			localized: true,
		},
		{
			name: "labelEmploymentType",
			type: "text",
			required: true,
			localized: true,
		},
	]),
};
