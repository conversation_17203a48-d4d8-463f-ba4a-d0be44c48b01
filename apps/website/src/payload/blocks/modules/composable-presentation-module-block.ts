import { componentsMapping } from "@/payload/blocks/components";
import { createModule<PERSON>lockFields } from "@/payload/fields/module-block-fields";
import { styledTitleField } from "@/payload/fields/styled-title-field";

import type { Block } from "payload";

const slotBlocks: Block[] = [
	/**
	 * Shows only an image.
	 */
	{
		slug: "image",
		fields: [
			{
				name: "image",
				type: "upload",
				required: true,
				relationTo: "media",
				filterOptions: {
					mimeType: {
						contains: "image",
					},
				},
			},
		],
	},

	/**
	 * Shows only an icon with a background.
	 */
	{
		slug: "image-icon",
		fields: [
			{
				name: "icon",
				type: "upload",
				required: true,
				relationTo: "media",
				filterOptions: {
					mimeType: {
						contains: "image",
					},
				},
			},
			{
				name: "size",
				type: "select",
				required: true,
				options: [
					{ value: "contained-default", label: "Contained (Default)" },
					{ value: "full", label: "Full" },
				],
			},
		],
	},
	{
		slug: "title",
		fields: [styledTitle<PERSON>ield({ name: "title", required: true })],
	},
	{
		slug: "title-icon",
		fields: [
			styledTitleField({ name: "title", required: true }),
			{
				name: "icon",
				type: "upload",
				required: true,
				relationTo: "media",
				filterOptions: {
					mimeType: {
						contains: "image",
					},
				},
			},
		],
	},
	{
		slug: "body",
		fields: [
			{
				name: "body",
				type: "richText",
				required: true,
			},
			{
				name: "actions",
				type: "blocks",
				required: false,
				maxRows: 1,
				minRows: 1,
				blocks: componentsMapping,
			},
		],
	},
	{
		slug: "title-body",
		fields: [
			styledTitleField({ name: "title", required: true }),
			{
				name: "body",
				type: "richText",
				required: true,
			},
			{
				name: "actions",
				type: "blocks",
				required: false,
				maxRows: 1,
				minRows: 1,
				blocks: componentsMapping,
			},
		],
	},
];

export const composablePresentationModuleBlock: Block = {
	slug: "composable-presentation-module",
	interfaceName: "ComposablePresentationModuleBlock",
	fields: createModuleBlockFields([
		{
			type: "group",
			name: "background",
			fields: [
				{
					type: "select",
					name: "type",
					required: true,
					defaultValue: "default",
					options: [
						{ value: "default", label: "Default" },
						{ value: "gradient", label: "Gradient" },
					],
				},
			],
		},
		{
			type: "blocks",
			name: "columns",
			maxRows: 2,
			minRows: 1,
			required: true,
			blocks: slotBlocks,
		},
		{
			type: "checkbox",
			name: "spanSingleColumn",
			label: "Span Single Column",
			defaultValue: false,
			required: true,
			admin: {
				description:
					"If checked, the first column will span the entire width. Only applicable if the number of columns is 1.",
			},
		},
	]),
};
