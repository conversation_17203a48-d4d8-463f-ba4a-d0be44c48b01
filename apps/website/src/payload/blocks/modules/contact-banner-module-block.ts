import { createModule<PERSON><PERSON>Fields } from "@/payload/fields/module-block-fields";
import { styledTitleField } from "@/payload/fields/styled-title-field";

import type { Block } from "payload";

export const contactBannerModuleBlock: Block = {
	slug: "contact-banner-module",
	interfaceName: "ContactBannerModuleBlock",
	fields: createModule<PERSON>lockFields([
		styledTitleField({
			name: "title",
			required: true,
			localized: true,
			admin: {
				description: `The title of the banner.`,
				disableListColumn: true,
			},
		}),
		{
			name: "body",
			type: "richText",
			required: true,
			localized: true,
			admin: {
				description: `The body of the banner.`,
				disableListColumn: true,
			},
		},

		{
			type: "relationship",
			name: "person",
			relationTo: "persons",
			required: true,
			hasMany: true,
			admin: {
				description: `The persons to display in the banner.`,
			},
		},
	]),
};
