import { createModuleBlockFields } from "@/payload/fields/module-block-fields";

import type { Block } from "payload";

export const personGridModuleBlock: Block = {
	slug: "person-grid-module",
	interfaceName: "PersonGridModuleBlock",
	fields: createModuleBlockFields([
		{
			name: "type",
			type: "select",
			required: true,
			defaultValue: "static",
			options: [
				{ value: "static", label: "Static" },
				{ value: "animated", label: "Animated" },
			],
		},
		{
			name: "persons",
			type: "group",
			fields: [
				{
					name: "type",
					type: "radio",
					required: true,
					defaultValue: "all",
					options: [
						{ value: "all", label: "All" },
						{ value: "specific", label: "Specific" },
					],
				},
				{
					name: "specific",
					label: "Specific Persons",
					type: "relationship",
					relationTo: "persons",
					hasMany: true,
					required: false,
					validate: (value, ctx) => {
						// Check if the value is an array with at least one element.
						const hasValue = !!value && value.length > 0;

						// If the type is specific, the value is required.
						if ((ctx.siblingData as any)?.type === "specific" && !hasValue) {
							return ctx.req.t("validation:required");
						}

						return true;
					},
					admin: {
						condition: (_: any, siblingData: any) =>
							siblingData?.type === "specific",
					},
				},
			],
		},

		{
			name: "maxRows",
			type: "number",
			label: "Max Rows",
			required: false,
			defaultValue: undefined,
			admin: {
				description:
					"The maximum number of rows to display. If not specified, there will be as many rows as needed to display the given persons.",
			},
		},
		{
			name: "animationUpdateInterval",
			type: "number",
			label: "Update Interval",
			required: true,
			defaultValue: 2.5,
			admin: {
				description:
					"The interval in seconds at which the persons are updated.",
				step: 0.5,
			},
		},
	]),
};
