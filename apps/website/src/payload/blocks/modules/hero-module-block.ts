import { componentsMapping } from "@/payload/blocks/components";
import { createModuleBlockFields } from "@/payload/fields/module-block-fields";
import { styledTitleField } from "@/payload/fields/styled-title-field";

import type { Block } from "payload";

export const heroModuleBlock: Block = {
	slug: "hero-module",
	interfaceName: "HeroModuleBlock",
	fields: createModuleBlockFields([
		styledTitleField({
			name: "title",
			required: true,
			localized: true,
		}),
		{
			name: "body",
			type: "richText",
			required: false,
			localized: true,
		},
		{
			name: "image",
			type: "upload",
			required: true,
			relationTo: "media",
			filterOptions: {
				mimeType: {
					contains: "image",
				},
			},
		},
		{
			name: "imageSize",
			type: "select",
			required: true,
			defaultValue: "full",
			options: [
				{ value: "contained-default", label: "Contained (Default)" },
				{ value: "full", label: "Full" },
			],
		},
		{
			name: "actions",
			type: "blocks",
			required: false,
			maxRows: 1,
			minRows: 1,
			blocks: componentsMapping,
		},
	]),
};
