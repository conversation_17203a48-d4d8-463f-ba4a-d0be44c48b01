import { linkField } from "@/payload/fields/link-field";
import { createModule<PERSON><PERSON>Fields } from "@/payload/fields/module-block-fields";
import { styledTitleField } from "@/payload/fields/styled-title-field";

import type { Block } from "payload";

export const presentationDualModuleBlock: Block = {
	slug: "presentation-dual-module",
	interfaceName: "PresentationDualModuleBlock",
	fields: createModuleBlockFields([
		{
			name: "items",
			type: "array",
			fields: [
				styledTitleField({
					name: "title",
					required: true,
					localized: true,
				}),
				{
					name: "body",
					type: "richText",
					required: false,
					localized: true,
				},
				linkField({ required: false }),
			],
		},
	]),
};
