import codes from "i18n-iso-countries/codes.json";
import codesEn from "i18n-iso-countries/langs/en.json";

import { createModuleBlockFields } from "@/payload/fields/module-block-fields";

import type { Block } from "payload";

const options = codes.map((entry) => {
	const alpha2Code = entry[0];
	let countryName = (codesEn.countries as any)[alpha2Code as any];
	if (Array.isArray(countryName)) {
		countryName = countryName[0];
	}

	return {
		label: countryName,
		value: alpha2Code,
	};
});

export const worldMapModuleBlock: Block = {
	slug: "world-map-module",
	interfaceName: "WorldMapModuleBlock",
	fields: createModuleBlockFields([
		{
			name: "highlightedCountries",
			type: "select",
			hasMany: true,
			options,
		},
	]),
};
