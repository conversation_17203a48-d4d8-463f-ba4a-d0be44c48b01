import { composablePresentationModuleBlock } from "@/payload/blocks/modules/composable-presentation-module-block";
import { contactBannerModuleBlock } from "@/payload/blocks/modules/contact-banner-module-block";
import { downloadModuleBlock } from "@/payload/blocks/modules/download-module-block";
import { dynamicGraphicsModuleBlock } from "@/payload/blocks/modules/dynamic-graphics-module-block";
import { featureGridModuleBlock } from "@/payload/blocks/modules/feature-grid-module-block";
import { heroModuleBlock } from "@/payload/blocks/modules/hero-module-block";
import { highlightGridModuleBlock } from "@/payload/blocks/modules/highlight-grid-module-block";
import { introModuleBlock } from "@/payload/blocks/modules/intro-module-block";
import { jobDetailsBodyModule } from "@/payload/blocks/modules/job-details-body-module";
import { jobDetailsHeroModule } from "@/payload/blocks/modules/job-details-hero-module";
import { jobsTableModuleBlock } from "@/payload/blocks/modules/jobs-table-module-block";
import { newsDetailsBodyModule } from "@/payload/blocks/modules/news-details-body-module";
import { newsDetailsHeroModule } from "@/payload/blocks/modules/news-details-hero-module";
import { newsListModuleBlock } from "@/payload/blocks/modules/news-list-module-block";
import { numbersGridModuleBlock } from "@/payload/blocks/modules/numbers-grid-module-block";
import { pageHeaderModuleBlock } from "@/payload/blocks/modules/page-header-module-block";
import { personGridModuleBlock } from "@/payload/blocks/modules/person-grid-module-block";
import { presentationDualModuleBlock } from "@/payload/blocks/modules/presentation-dual-module-block";
import { presentationItemsModuleBlock } from "@/payload/blocks/modules/presentation-items-module-block";
import { presentationModuleBlock } from "@/payload/blocks/modules/presentation-module-block";
import { projectDetailsBodyModule } from "@/payload/blocks/modules/project-details-body-module-block";
import { projectDetailsFactsModule } from "@/payload/blocks/modules/project-details-facts-module";
import { projectDetailsHeroModule } from "@/payload/blocks/modules/project-details-hero-module";
import { projectDetailsImagesModule } from "@/payload/blocks/modules/project-details-images-module";
import { projectsListModuleBlock } from "@/payload/blocks/modules/projects-list-module-block";
import { projectsSliderModuleBlock } from "@/payload/blocks/modules/projects-slider-module-block";
import { sectionHeaderModuleBlock } from "@/payload/blocks/modules/section-header-module-block";
import { typographyShowcaseModuleBlock } from "@/payload/blocks/modules/typography-showcase-module-block";
import { valuesGridModuleBlock } from "@/payload/blocks/modules/values-grid-module-block";
import { worldMapModuleBlock } from "@/payload/blocks/modules/world-map-module-block";

// Array, which contains all the module blocks.
export const moduleBlocks = [
	heroModuleBlock,
	numbersGridModuleBlock,
	sectionHeaderModuleBlock,
	presentationModuleBlock,
	featureGridModuleBlock,
	introModuleBlock,
	highlightGridModuleBlock,
	presentationDualModuleBlock,
	projectsSliderModuleBlock,
	contactBannerModuleBlock,
	pageHeaderModuleBlock,
	typographyShowcaseModuleBlock,
	presentationItemsModuleBlock,
	composablePresentationModuleBlock,
	downloadModuleBlock,
	worldMapModuleBlock,
	projectsListModuleBlock,
	personGridModuleBlock,
	dynamicGraphicsModuleBlock,
	projectDetailsHeroModule,
	projectDetailsImagesModule,
	projectDetailsFactsModule,
	projectDetailsBodyModule,
	valuesGridModuleBlock,
	newsDetailsHeroModule,
	newsDetailsBodyModule,
	newsListModuleBlock,
	jobsTableModuleBlock,
	jobDetailsHeroModule,
	jobDetailsBodyModule,
];
