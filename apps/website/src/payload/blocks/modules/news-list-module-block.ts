import { createModuleBlockFields } from "@/payload/fields/module-block-fields";

import type { Block } from "payload";

export const newsListModuleBlock: Block = {
	slug: "news-list-module",
	interfaceName: "NewsListModuleBlock",
	fields: createModuleBlockFields([
		{
			name: "pageSize",
			type: "number",
			label: "Page Size",
			defaultValue: 8,
			min: 3,
			max: 16,
			required: true,
		},
		{
			name: "categoryTranslations",
			type: "array",
			required: false,
			minRows: 0,
			maxRows: 1,
			fields: [
				{
					name: "key",
					type: "text",
					required: true,
				},
				{
					name: "value",
					type: "text",
					required: true,
					localized: true,
				},
			],
		},
	]),
};
