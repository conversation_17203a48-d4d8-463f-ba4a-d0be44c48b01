import { linkField } from "@/payload/fields/link-field";
import { createModule<PERSON><PERSON>Fields } from "@/payload/fields/module-block-fields";

import type { Block } from "payload";

export const highlightGridModuleBlock: Block = {
	slug: "highlight-grid-module",
	interfaceName: "HighlightGridModuleBlock",
	fields: createModuleBlockFields([
		{
			name: "items",
			type: "array",
			required: true,
			minRows: 1,
			admin: {
				components: {
					RowLabel: {
						path: "@/payload/admin-components/CustomRowLabel#CustomRowLabel",
						clientProps: { field: "label" },
					},
				},
			},
			fields: [
				{
					name: "title",
					type: "richText",
					required: true,
					localized: true,
				},
				{
					name: "body",
					type: "richText",
					required: false,
					localized: true,
				},
				{
					name: "image",
					type: "upload",
					required: true,
					relationTo: "media",
					filterOptions: {
						mimeType: {
							contains: "image",
						},
					},
				},
				linkField({ required: false, withLabel: true }),
			],
		},
	]),
};
