import { componentsMapping } from "@/payload/blocks/components";
import { createModuleBlockFields } from "@/payload/fields/module-block-fields";
import { styledTitleField } from "@/payload/fields/styled-title-field";

import type { Block } from "payload";

export const presentationModuleBlock: Block = {
	slug: "presentation-module",
	interfaceName: "PresentationModuleBlock",
	fields: createModuleBlockFields([
		styledTitleField({
			name: "title",
			required: true,
			localized: true,
		}),
		{
			name: "body",
			type: "richText",
			required: false,
			localized: true,
		},
		{
			name: "image",
			type: "upload",
			required: true,
			relationTo: "media",
			filterOptions: {
				mimeType: {
					contains: "image",
				},
			},
		},
		{
			type: "select",
			name: "image-placement",
			options: ["start", "end", "bg-container"],
			required: true,
			defaultValue: "start",
		},
		{
			name: "actions",
			type: "blocks",
			required: false,
			maxRows: 1,
			minRows: 1,
			blocks: componentsMapping,
		},
	]),
};
