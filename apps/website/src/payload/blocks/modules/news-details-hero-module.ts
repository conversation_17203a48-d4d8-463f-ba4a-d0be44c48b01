import { createModuleBlockFields } from "@/payload/fields/module-block-fields";

import type { Block } from "payload";

export const newsDetailsHeroModule: Block = {
	slug: "news-details-hero-module",
	interfaceName: "NewsDetailsHeroModuleBlock",
	fields: createModuleBlockFields([
		{
			name: "categoryTranslations",
			type: "array",
			required: false,
			minRows: 0,
			maxRows: 1,
			fields: [
				{
					name: "key",
					type: "text",
					required: true,
				},
				{
					name: "value",
					type: "text",
					required: true,
					localized: true,
				},
			],
		},
	]),
};
