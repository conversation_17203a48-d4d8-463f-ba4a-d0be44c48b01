import { createModuleBlockFields } from "@/payload/fields/module-block-fields";

import type { Block } from "payload";

export const numbersGridModuleBlock: Block = {
	slug: "numbers-grid-module",
	interfaceName: "NumbersGridModuleBlock",
	fields: createModuleBlockFields([
		{
			name: "perRow",
			type: "select",
			required: true,
			defaultValue: "3",
			options: [
				{
					label: "3",
					value: "3",
				},
				{
					label: "4",
					value: "4",
				},
			],
			admin: {
				description: "Number of items to display per row (desktop only).",
			},
		},
		{
			name: "items",
			type: "array",
			required: true,
			minRows: 3,
			maxRows: 8,
			admin: {
				components: {
					RowLabel: {
						path: "@/payload/admin-components/CustomRowLabel#CustomRowLabel",
						clientProps: {
							field: "prefix",
						},
					},
				},
			},
			fields: [
				{
					name: "number",
					type: "number",
					required: true,
				},
				{
					name: "prefix",
					type: "text",
					required: true,
					localized: true,
				},
				{
					name: "suffix",
					type: "text",
					required: true,
					localized: true,
				},
				{
					name: "decimals",
					type: "number",
					required: false,
					min: 0,
					max: 10,
				},
			],
		},
	]),
};
