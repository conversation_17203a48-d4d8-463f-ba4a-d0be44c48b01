import { createModuleBlockFields } from "@/payload/fields/module-block-fields";

import type { Block } from "payload";

export const valuesGridModuleBlock: Block = {
	slug: "values-grid-module",
	interfaceName: "ValuesGridModuleBlock",
	fields: createModuleBlockFields([
		{
			name: "placeholderImage",
			type: "upload",
			required: true,
			relationTo: "media",
			filterOptions: {
				mimeType: {
					contains: "image",
				},
			},
		},
		{
			name: "items",
			type: "array",
			required: true,
			minRows: 1,
			maxRows: 5,
			admin: {
				components: {
					RowLabel: {
						path: "@/payload/admin-components/CustomRowLabel#CustomRowLabel",
						clientProps: { field: "label" },
					},
				},
			},
			fields: [
				{
					name: "title",
					type: "richText",
					required: true,
					localized: true,
				},
				{
					name: "body",
					type: "richText",
					required: true,
					localized: true,
				},
				{
					name: "image",
					type: "upload",
					required: true,
					relationTo: "media",
					filterOptions: {
						mimeType: {
							contains: "image",
						},
					},
				},
			],
		},
	]),
};
