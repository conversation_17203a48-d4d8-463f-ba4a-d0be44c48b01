import { createModule<PERSON><PERSON>Fields } from "@/payload/fields/module-block-fields";
import { styledTitleField } from "@/payload/fields/styled-title-field";

import type { Block } from "payload";

export const downloadModuleBlock: Block = {
	slug: "download-module",
	interfaceName: "DownloadModuleBlock",
	fields: createModule<PERSON>lockFields([
		styledTitleField({
			name: "title",
			required: true,
			localized: true,
			admin: {
				description: `The title of the banner.`,
				disableListColumn: true,
			},
		}),
		{
			type: "row",
			fields: [
				{
					name: "fileLabel",
					type: "text",
					required: true,
				},
				{
					name: "file",
					type: "upload",
					required: true,
					hasMany: false,
					relationTo: "media",
					admin: {
						description: `The file to download.`,
					},
				},
			],
		},
	]),
};
