import { createModuleBlockFields } from "@/payload/fields/module-block-fields";

import type { Block } from "payload";

export const typographyShowcaseModuleBlock: Block = {
	slug: "typography-showcase-module",
	interfaceName: "TypographyShowcaseModuleBlock",
	fields: createModuleBlockFields([
		{
			name: "body",
			type: "richText",
			required: true,
			localized: true,
			admin: {
				description: `The body of the module.`,
				disableListColumn: true,
			},
		},
	]),
};
