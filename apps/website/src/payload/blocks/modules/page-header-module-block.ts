import { createModuleBlockFields } from "@/payload/fields/module-block-fields";
import { styledTitleField } from "@/payload/fields/styled-title-field";

import type { Block } from "payload";

export const pageHeaderModuleBlock: Block = {
	slug: "page-header-module",
	interfaceName: "PageHeaderModuleBlock",
	fields: createModuleBlockFields([
		styledTitleField({
			name: "title",
			required: true,
			localized: true,
		}),
	]),
};
