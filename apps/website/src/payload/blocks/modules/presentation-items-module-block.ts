import { componentsMapping } from "@/payload/blocks/components";
import { createModuleBlockFields } from "@/payload/fields/module-block-fields";
import { styledTitleField } from "@/payload/fields/styled-title-field";

import type { Block } from "payload";

export const presentationItemsModuleBlock: Block = {
	slug: "presentation-items-module",
	interfaceName: "PresentationItemsModuleBlock",
	fields: createModule<PERSON>lockFields([
		{
			name: "items",
			type: "array",
			minRows: 2,
			maxRows: 3,
			fields: [
				styledTitleField({
					name: "title",
					required: true,
					localized: true,
				}),
				{
					name: "titleUppercase",
					type: "checkbox",
					required: true,
					defaultValue: true,
				},
				{
					name: "body",
					type: "richText",
					required: false,
					localized: true,
				},
				{
					name: "actions",
					type: "blocks",
					required: false,
					maxRows: 1,
					minRows: 1,
					blocks: componentsMapping,
				},
				{
					name: "icon",
					type: "upload",
					relationTo: "media",
					required: false,
					filterOptions: {
						mimeType: {
							contains: "image",
						},
					},
				},
			],
		},
	]),
};
