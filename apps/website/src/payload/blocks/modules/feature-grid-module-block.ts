import { createModuleBlockFields } from "@/payload/fields/module-block-fields";
import { styledTitleField } from "@/payload/fields/styled-title-field";

import type { Block } from "payload";

export const featureGridModuleBlock: Block = {
	slug: "feature-grid-module",
	interfaceName: "FeatureGridModuleBlock",
	fields: createModuleBlockFields([
		styledTitleField({
			name: "title",
			required: true,
			localized: true,
		}),
		{
			name: "body",
			type: "richText",
			required: false,
			localized: true,
		},
		{
			name: "image",
			type: "upload",
			required: true,
			relationTo: "media",
			filterOptions: {
				mimeType: {
					contains: "image",
				},
			},
		},
	]),
};
