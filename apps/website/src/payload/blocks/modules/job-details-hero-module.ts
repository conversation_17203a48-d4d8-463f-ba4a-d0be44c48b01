import { createModuleBlockFields } from "@/payload/fields/module-block-fields";

import type { Block } from "payload";

export const jobDetailsHeroModule: Block = {
	slug: "job-details-hero-module",
	interfaceName: "JobDetailsHeroModuleBlock",
	fields: createModuleBlockFields([
		{
			name: "labelEntryLevel",
			type: "text",
			required: true,
			localized: true,
		},
		{
			name: "labelEmploymentType",
			type: "text",
			required: true,
			localized: true,
		},
		{
			name: "labelStartDate",
			type: "text",
			required: true,
			localized: true,
		},
		{
			name: "labelTimeLimitation",
			type: "text",
			required: true,
			localized: true,
		},
		{
			name: "labelLocation",
			type: "text",
			required: true,
			localized: true,
		},
		{
			name: "labelDepartment",
			type: "text",
			required: true,
			localized: true,
		},
	]),
};
