import { componentsMapping } from "@/payload/blocks/components";
import { createModuleBlockFields } from "@/payload/fields/module-block-fields";
import { styledTitleField } from "@/payload/fields/styled-title-field";

import type { Block } from "payload";

export const sectionHeaderModuleBlock: Block = {
	slug: "section-header-module",
	interfaceName: "SectionHeaderModuleBlock",
	fields: createModuleBlockFields([
		styledTitleField({
			name: "title",
			required: true,
			localized: true,
		}),
		{
			name: "body",
			type: "richText",
			required: false,
			localized: true,
		},
		{
			name: "actions",
			type: "blocks",
			required: false,
			maxRows: 1,
			minRows: 1,
			blocks: componentsMapping,
		},
	]),
};
