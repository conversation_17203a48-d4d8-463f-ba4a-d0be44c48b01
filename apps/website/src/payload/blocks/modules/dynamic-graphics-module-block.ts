import { createModuleBlockFields } from "@/payload/fields/module-block-fields";

import type { Block } from "payload";

export const dynamicGraphicsModuleBlock: Block = {
	slug: "dynamic-graphics-module",
	interfaceName: "DynamicGraphicsModuleBlock",
	fields: createModuleBlockFields([
		{
			name: "items",
			type: "array",
			required: true,
			minRows: 1,
			maxRows: 4,
			fields: [
				{
					name: "image",
					type: "upload",
					required: true,
					relationTo: "media",
					admin: {
						description: "Only SVG files are supported.",
					},
					filterOptions: {
						mimeType: {
							contains: "image/svg",
						},
					},
				},
			],
		},
	]),
};
