import { componentsMapping } from "@/payload/blocks/components";
import { createModuleBlockFields } from "@/payload/fields/module-block-fields";
import { styledTitleField } from "@/payload/fields/styled-title-field";

import type { Block } from "payload";

export const introModuleBlock: Block = {
	slug: "intro-module",
	interfaceName: "IntroModuleBlock",
	fields: createModuleBlockFields([
		styledTitleField({
			name: "title",
			required: true,
			localized: true,
		}),
		{
			name: "body",
			type: "richText",
			required: false,
			localized: true,
		},
		{
			name: "withDivider",
			type: "checkbox",
			required: true,
			defaultValue: true,
		},
		{
			name: "actions",
			type: "blocks",
			required: false,
			maxRows: 1,
			minRows: 1,
			blocks: componentsMapping,
		},
		{
			name: "actionsPlacement",
			type: "select",
			required: true,
			defaultValue: "below-body",
			options: [
				{
					value: "below-body",
					label: "Below Body",
				},
				{
					value: "standalone",
					label: "Standalone",
				},
			],
		},
	]),
};
