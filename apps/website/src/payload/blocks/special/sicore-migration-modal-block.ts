import { styledTitleField } from "@/payload/fields/styled-title-field";

import type { Block } from "payload";

/**
 * This is a generic block that represents a logical section.
 * It may contain multiple child "module" blocks.
 */
export const sicoreMigrationModalBlock: Block = {
	slug: "sicore-migration-modal",
	interfaceName: "SicoreMigrationModalBlock",
	fields: [
		styledTitleField({
			name: "title",
			required: true,
			localized: true,
			admin: {
				description: `The title of the modal.`,
				disableListColumn: true,
			},
		}),
		{
			name: "body",
			type: "richText",
			localized: true,
		},
	],
};
