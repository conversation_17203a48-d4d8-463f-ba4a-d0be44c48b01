import { moduleBlocks } from "@/payload/blocks/modules";

import type { Block } from "payload";

/**
 * This is a generic block that represents a logical section.
 * It may contain multiple child "module" blocks.
 */
export const sectionWrapperBlock: Block = {
	slug: "section-wrapper",
	interfaceName: "SectionWrapperBlock",
	fields: [
		{
			type: "tabs",
			tabs: [
				{
					label: "General",
					fields: [
						{
							name: "identifier",
							type: "text",
							required: false,
							admin: {
								description: `HTML 'id' attribute of the section`,
							},
						},
						{
							name: "modules",
							type: "blocks",
							required: true,
							minRows: 1,
							maxRows: 15,

							// Only modules are allowed as children.
							blocks: moduleBlocks,
						},
					],
				},
				{
					label: "Layout",
					name: "layout",
					fields: [
						{
							name: "showDivider",
							type: "checkbox",
							defaultValue: true,
						},
					],
				},
			],
		},
	],
};
