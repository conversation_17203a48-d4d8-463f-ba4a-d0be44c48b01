import { linkField } from "@/payload/fields/link-field";
import { socialNetworkOptions } from "@/payload/fields/utils";

import type { Block } from "payload";

export const footerBlock: Block = {
	slug: "footer",
	interfaceName: "FooterBlock",
	fields: [
		{
			name: "details",
			type: "richText",
			required: true,
			localized: true,
		},
		{
			name: "groups",
			type: "array",
			required: true,
			maxRows: 15,
			minRows: 1,
			admin: {
				components: {
					RowLabel: {
						path: "@/payload/admin-components/CustomRowLabel#CustomRowLabel",
						clientProps: { field: "title", childrenField: "items" },
					},
				},
			},
			fields: [
				{
					type: "text",
					name: "title",
					required: false,
					localized: true,
				},
				{
					type: "array",
					name: "items",
					required: true,
					admin: {
						components: {
							RowLabel: {
								path: "@/payload/admin-components/CustomRowLabel#CustomRowLabel",
								clientProps: {
									field: "label",
								},
							},
						},
					},
					fields: [
						{
							type: "text",
							name: "label",
							required: true,
							localized: true,
						},
						linkField({}),
					],
				},
			],
		},
		{
			name: "bottomLine",
			type: "group",
			fields: [
				{
					name: "title",
					type: "text",
					required: true,
					localized: true,
				},
				{
					name: "socialNetworks",
					type: "array",
					required: true,
					admin: {
						components: {
							RowLabel: {
								path: "@/payload/admin-components/CustomRowLabel#CustomRowLabel",
								clientProps: { field: "type" },
							},
						},
					},
					fields: [
						{
							type: "radio",
							name: "type",
							required: true,
							options: socialNetworkOptions,
						},
						linkField({}),
					],
				},
			],
		},
	],
};
