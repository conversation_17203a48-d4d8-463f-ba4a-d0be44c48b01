import { createCollection<PERSON>tring<PERSON>ield } from "@/payload/fields/collection-string-field";
import { linkField } from "@/payload/fields/link-field";

import type { Block, GroupField } from "payload";

const activeStateField: GroupField = {
	type: "group",
	name: "activeState",
	interfaceName: "HeaderBlockActiveState",
	admin: {
		description: `Configuration for the active state of this item.`,
	},
	fields: [
		createCollectionStringField({
			name: "collection",
			required: false,
			admin: {
				description: `The collection to include in the active state.`,
			},
		}),
	],
};

export const headerBlock: Block = {
	slug: "header",
	interfaceName: "HeaderBlock",
	fields: [
		{
			type: "array",
			name: "items",
			required: true,
			minRows: 1,
			admin: {
				components: {
					RowLabel: {
						path: "@/payload/admin-components/CustomRowLabel#CustomRowLabel",
						clientProps: {
							field: "label",
							childrenField: "children",
						},
					},
				},
			},
			fields: [
				{
					type: "text",
					name: "label",
					required: true,
					localized: true,
				},
				linkField({ required: false }),
				activeStateField,
				{
					type: "array",
					name: "children",
					required: false,
					admin: {
						components: {
							RowLabel: {
								path: "@/payload/admin-components/CustomRowLabel#CustomRowLabel",
								clientProps: {
									field: "label",
								},
							},
						},
					},
					fields: [
						{
							type: "text",
							name: "label",
							required: true,
							localized: true,
						},
						linkField({}),
						activeStateField,
					],
				},
			],
		},
		{
			type: "array",
			name: "socials",
			required: false,
			minRows: 0,
			fields: [
				{
					type: "select",
					required: true,
					name: "type",
					options: [
						{ label: "LinkedIn", value: "linkedin" },
						{ label: "Xing", value: "xing" },
					],
				},
				linkField({ required: true }),
			],
		},
		{
			type: "group",
			name: "configuration",
			fields: [
				{
					type: "checkbox",
					name: "enableSearch",
					required: true,
					defaultValue: true,
				},
			],
		},
	],
};
