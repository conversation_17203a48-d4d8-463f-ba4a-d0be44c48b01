import {
	createPageShellLayoutTab,
	createPageShellMetaTab,
} from "@/payload/fields/page-shell-fields";
import { createCollectionAccessControl } from "@/payload/utils/access-control";

import type { GlobalConfig } from "payload";

export const newsShellGlobal: GlobalConfig<"news-shell"> = {
	slug: "news-shell",
	versions: { drafts: true },
	access: createCollectionAccessControl(),
	fields: [
		{
			type: "tabs",
			tabs: [
				createPageShellLayoutTab(),
				createPageShellMetaTab({ referencingCollection: "news" }),
			],
		},
	],
};
