import {
	createPageShellLayoutTab,
	createPageShellMetaTab,
} from "@/payload/fields/page-shell-fields";
import { createCollectionAccessControl } from "@/payload/utils/access-control";

import type { GlobalConfig } from "payload";

export const projectsShellGlobal: GlobalConfig<"projects-shell"> = {
	slug: "projects-shell",
	versions: { drafts: true },
	access: createCollectionAccessControl(),
	fields: [
		{
			type: "tabs",
			tabs: [
				createPageShellLayoutTab(),
				createPageShellMetaTab({ referencingCollection: "projects" }),
			],
		},
	],
};
