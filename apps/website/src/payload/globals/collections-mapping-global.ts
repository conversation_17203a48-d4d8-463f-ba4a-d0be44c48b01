import { revalidateTag } from "next/cache";
import { pathToRegexp } from "path-to-regexp";
import { text } from "payload/shared";

import { createCollectionStringField } from "@/payload/fields/collection-string-field";
import { createCollectionAccessControl } from "@/payload/utils/access-control";
import { payloadCacheTags } from "@/utils/cache";

import type { CollectionSlug, GlobalConfig, TextField } from "payload";

export const collectionsMappingGlobal: GlobalConfig<"collections-mapping"> = {
	slug: "collections-mapping",
	access: createCollectionAccessControl(),
	hooks: {
		afterChange: [
			() => {
				// We also need to revalidate the lookup cache.
				revalidateTag(payloadCacheTags.collectionMapping);
			},
		],
	},
	fields: [
		{
			type: "array",
			name: "collections",
			minRows: 0,
			required: true,
			validate: (value) => {
				// Check if there are any duplicate collections.
				const collectionSlugs =
					(value as any[])?.map((v) => v.collection) ?? [];

				if (new Set(collectionSlugs).size !== collectionSlugs.length) {
					return "Duplicate collections";
				}

				return true;
			},
			fields: [
				createCollectionStringField({
					name: "collection",
					required: true,
				}),
				{
					type: "text",
					name: "path",
					required: true,
					localized: true,
					admin: {
						description: "Path of the collection. E.g. /projects/:slug",
					},
					validate: (value, opts) => {
						// Call the internal validation first for "required", etc.
						const upValidate = text(value, opts);
						if (upValidate !== true) {
							return upValidate;
						}

						if (typeof value !== "string") {
							return "Path is invalid";
						}

						const collectionName = (opts.siblingData as any)
							.collection as CollectionSlug;
						let ptrParsed;

						// Try to parse the path to an expression.
						try {
							ptrParsed = pathToRegexp(value);
						} catch (_) {
							return "Path could not be parsed to an expression";
						}

						// The path must contain exactly one parameter.
						if (ptrParsed.keys.length !== 1) {
							return "Path must contain exactly one parameter";
						}

						// Resolve the name of the first key.
						const keyName = ptrParsed.keys[0].name;

						// Validate that the collection defines a field with the same name.
						const collectionConfig =
							opts.req.payload.collections[collectionName]?.config;

						// The collection must be available to scan the config.
						if (!collectionConfig) {
							return "Unable to resolve collection config";
						}

						// Find the field with the same name.
						const matchingField = collectionConfig.flattenedFields.find((f) => {
							const fieldName = (f as any).name;
							if (!fieldName) {
								return false;
							}

							return fieldName === keyName;
						});

						// The collection must define a field with the same name.
						if (!matchingField) {
							return `Collection does not define a field with the name "${keyName}"`;
						}

						return true;
					},
				} satisfies TextField,
			],
		},
	],
};
