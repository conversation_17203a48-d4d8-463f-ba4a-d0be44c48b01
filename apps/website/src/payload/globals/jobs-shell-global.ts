import {
	createPageShellLayoutTab,
	createPageShellMetaTab,
} from "@/payload/fields/page-shell-fields";
import { createCollectionAccessControl } from "@/payload/utils/access-control";

import type { GlobalConfig } from "payload";

export const jobsShellGlobal: GlobalConfig<"jobs-shell"> = {
	slug: "jobs-shell",
	versions: { drafts: true },
	access: createCollectionAccessControl(),
	fields: [
		{
			type: "tabs",
			tabs: [
				createPageShellLayoutTab(),
				createPageShellMetaTab({ referencingCollection: "jobs" }),
			],
		},
	],
};
