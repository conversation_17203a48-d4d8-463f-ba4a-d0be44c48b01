import { revalidateTag } from "next/cache";

import {
	createPageShellLayoutTab,
	createPageShellMetaTab,
} from "@/payload/fields/page-shell-fields";
import { createCollectionAccessControl } from "@/payload/utils/access-control";
import { buildPayloadPageCacheTags, payloadCacheTags } from "@/utils/cache";

import type { Page } from "@/gen/payload/types";
import type { CollectionAfterChangeHook, CollectionConfig } from "payload";

/**
 * Hook to revalidate the page cache after a change.
 */
const revalidateHook: CollectionAfterChangeHook<Page> = async (args) => {
	const pageCacheTags = buildPayloadPageCacheTags({
		path: args.doc.slug,
	});

	// If there are any cache tags, revalidate the last one.
	// The last one is always the most scoped-down one.
	// We try to avoid revalidating the global cache tag.
	if (pageCacheTags.length > 0) {
		revalidateTag(pageCacheTags[pageCacheTags.length - 1]);
	}

	// We also need to revalidate the lookup cache.
	revalidateTag(payloadCacheTags.lookup);
};

export const pagesCollection: CollectionConfig<"pages"> = {
	slug: "pages",
	versions: { drafts: true },
	admin: {
		useAsTitle: "title",
		defaultColumns: ["title", "slug"],
	},
	hooks: {
		afterChange: [revalidateHook],
	},
	defaultPopulate: {
		slug: true,
		title: true,
		meta: true,
	},
	access: createCollectionAccessControl(),
	fields: [
		{
			type: "tabs",
			tabs: [
				{
					label: "General",
					fields: [
						{
							name: "title",
							type: "text",
							required: true,
							admin: {
								description: `The title of the page. This is only used to identify the page in the UI.\nSee the "SEO" to set the title of the page.`,
							},
						},
						{
							name: "slug",
							type: "text",
							required: true,
							unique: true,
							localized: true,
							index: true,
							validate: (value: any) => {
								const defaultMessage = "Slug is invalid";
								if (typeof value !== "string") {
									return defaultMessage;
								}

								const regexp = /^\/(?:[A-Za-z0-9-]+(?:\/[A-Za-z0-9-]+)*)?$/;
								if (!regexp.test(value)) {
									return defaultMessage;
								}

								return true;
							},
							admin: {
								description: `The URL slug of the page. This must start with a forward slash and can only contain alphanumeric characters and dashes.`,
							},
						},
					],
				},
				createPageShellLayoutTab(),
				createPageShellMetaTab(),
			],
		},
	],
};
