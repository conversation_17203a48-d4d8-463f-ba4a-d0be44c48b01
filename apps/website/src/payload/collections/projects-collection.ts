import { slugField } from "@/payload/fields/slug-field";
import { createCollectionAccessControl } from "@/payload/utils/access-control";

import type { CollectionConfig } from "payload";

export const projectsCollection: CollectionConfig = {
	slug: "projects",
	versions: { drafts: true },
	admin: {
		useAsTitle: "title",
		defaultColumns: ["title", "slug"],
	},
	access: createCollectionAccessControl(),
	fields: [
		{
			type: "tabs",
			tabs: [
				{
					label: "General",
					fields: [
						slugField(
							{ trackingFields: ["title", "addressCity"] },
							{
								required: true,
								unique: true,
								localized: true,
								index: true,
								admin: {
									description: `The URL slug of the project. This must only contain alphanumeric characters and dashes.`,
								},
							},
						),
						{
							name: "title",
							type: "text",
							required: true,
							admin: {
								description: `The title of the project.`,
							},
						},
						{
							name: "shortDescription",
							type: "textarea",
							required: false,
							localized: true,
							maxLength: 256,
							admin: {
								description: `A short description of the project. Should not exceed 256 characters.`,
								disableListColumn: true,
							},
						},
						{
							name: "description",
							type: "richText",
							required: false,
							localized: true,
							admin: {
								disableListColumn: true,
								description: `Description of the project, which is shown on the project details page.`,
							},
						},
						{
							name: "facts",
							type: "array",
							required: false,
							minRows: 0,
							admin: {
								components: {
									RowLabel: {
										path: "@/payload/admin-components/CustomRowLabel#CustomRowLabel",
										clientProps: { field: "key" },
									},
								},
							},
							fields: [
								{
									name: "key",
									type: "text",
									required: true,
									localized: true,
								},
								{
									name: "value",
									type: "text",
									required: true,
									localized: true,
								},
							],
						},
					],
				},
				{
					label: "Location",
					fields: [
						{
							name: "addressFull",
							type: "text",
							required: false,
							localized: true,
							admin: {
								description: `Full address of the project. E.g. "Reeperbahn 1, 20359 Hamburg, DE"`,
							},
						},
						{
							name: "addressCity",
							type: "text",
							required: false,
							localized: true,
							admin: {
								description: `City of the project's address. E.g. "Hamburg"`,
							},
						},
					],
				},
				{
					label: "Images",
					fields: [
						{
							name: "coverImage",
							type: "upload",
							required: true,
							relationTo: "media",
							filterOptions: {
								mimeType: {
									contains: "image",
								},
							},
							admin: {
								disableListColumn: true,
							},
						},
						{
							name: "allImages",
							type: "upload",
							relationTo: "media",
							hasMany: true,
							maxRows: 16,
							required: false,
							filterOptions: {
								mimeType: {
									contains: "image",
								},
							},
							admin: {
								disableListColumn: true,
							},
						},
					],
				},
			],
		},
	],
};
