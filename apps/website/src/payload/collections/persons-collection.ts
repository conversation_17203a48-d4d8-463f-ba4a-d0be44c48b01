import { slugField } from "@/payload/fields/slug-field";
import { createCollectionAccessControl } from "@/payload/utils/access-control";

import type { CollectionConfig } from "payload";

export const personsCollection: CollectionConfig = {
	slug: "persons",
	versions: { drafts: true },
	admin: {
		useAsTitle: "title",
		defaultColumns: ["title", "slug"],
	},
	access: createCollectionAccessControl(),
	fields: [
		slugField(
			{ trackingFields: ["title"] },
			{
				localized: true,
				required: true,
				unique: true,
				index: true,
			},
		),
		{
			type: "tabs",
			tabs: [
				{
					label: "General",
					fields: [
						{
							name: "title",
							type: "text",
							required: true,
							admin: {
								description: `Name of the person.`,
							},
						},
						{
							name: "jobTitle",
							type: "text",
							required: false,
						},
						{
							name: "emailAddress",
							type: "text",
							required: false,
						},
						{
							name: "phoneNumber",
							type: "text",
							required: false,
						},
						{
							name: "image",
							type: "upload",
							required: true,
							relationTo: "media",
							filterOptions: { mimeType: { contains: "image" } },
							admin: {
								disableListColumn: true,
								description: "Image of the person. Should be a square image.",
							},
						},
					],
				},
				{
					label: "Advanced",
					fields: [
						{
							name: "excludeFromDisplay",
							type: "checkbox",
							required: true,
							defaultValue: false,
							admin: {
								description: `If this is checked, the person will not be displayed on person pool modules.`,
							},
						},
					],
				},
			],
		},
	],
};
