import { createCollectionAccessControl } from "@/payload/utils/access-control";

import type { CollectionConfig } from "payload";

export const usersCollection: CollectionConfig = {
	slug: "users",
	admin: {
		useAsTitle: "email",
	},
	access: createCollectionAccessControl(),
	auth: true,
	fields: [
		{
			name: "role",
			type: "select",
			options: [
				{ label: "Admin", value: "admin" },
				{ label: "Read Only", value: "read-only" },
			],
			required: true,
			defaultValue: "admin",
		},
	],
};
