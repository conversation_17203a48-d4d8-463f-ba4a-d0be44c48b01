import { convertHtmlToLexical } from "@/payload/utils/lexical-converter";
import { slugifyString } from "@/utils/slugify";

import type { Job } from "@/gen/payload/types";
import type { CollectionBeforeChangeHook, CollectionConfig } from "payload";

/**
 * Handles data population for the jobs collection.
 * @param data
 */
const handleDataPopulation: CollectionBeforeChangeHook<Job> = async ({
	data,
}) => {
	if (data.data) {
		const rawData = data.data as any;

		if (rawData.title) {
			data.title = rawData.title;
		}

		if (rawData.description) {
			try {
				// Try to convert the HTML to Lexical.
				const lexicalData = await convertHtmlToLexical(rawData.description);

				// If the conversion was successful, set the description.
				data.description = lexicalData as any;
			} catch (_) {
				console.warn("Failed to convert HTML to Lexical");
			}
		}

		if (rawData.entryLevel) {
			data.entryLevel = rawData.entryLevel;
		}

		if (rawData.employmentType) {
			data.employmentType = rawData.employmentType;
		}

		if (rawData.startDate) {
			data.startDate = rawData.startDate;
		}

		if (rawData.timeLimitation) {
			data.timeLimitation = rawData.timeLimitation;
		}

		if (rawData.location) {
			data.location = rawData.location;
		}

		if (rawData.department) {
			data.department = rawData.department;
		}

		if (rawData.externalLink) {
			data.externalLink = rawData.externalLink;
		}

		// Create the slug from the identifier and the title.
		// This is used to create a unique slug for the job and avoid any clash.
		data.slug = `${rawData.id}-${slugifyString(rawData.title)}`;
	}
};

export const jobsCollection: CollectionConfig = {
	slug: "jobs",
	admin: {
		useAsTitle: "title",
		components: {
			views: {
				list: {
					actions: [
						{
							path: "@/payload/admin-components/JobsSyncAction#JobsSyncAction",
							clientProps: {},
						},
					],
				},
			},
		},
	},
	access: {
		create: () => false,
		update: () => false,
	},
	hooks: {
		beforeChange: [handleDataPopulation],
	},
	fields: [
		{
			/**
			 * We use a custom ID field for the jobs, which is provided externally.
			 */
			name: "id",
			label: "ID",
			type: "text",
			required: true,
			admin: { hidden: true },
			unique: true,
		},
		{
			name: "slug",
			unique: true,
			type: "text",
		},
		{
			type: "tabs",
			tabs: [
				{
					label: "General",
					fields: [
						{
							name: "title",
							type: "text",
							required: false,
							admin: {
								description:
									"Title of the Job. This is automatically generated.",
							},
						},
						{
							name: "description",
							type: "richText",
							required: false,
							admin: {
								description:
									"Description of the Job. This is automatically generated.",
								disableListColumn: true,
							},
						},
						{
							name: "entryLevel",
							type: "text",
							required: false,
							admin: {
								description:
									"Entry level of the Job. This is automatically generated.",
							},
						},
						{
							name: "employmentType",
							type: "text",
							required: false,
							admin: {
								description:
									"Employment type of the Job. This is automatically generated.",
							},
						},
						{
							name: "location",
							type: "text",
							required: false,
							admin: {
								description:
									"Location of the Job. This is automatically generated.",
							},
						},
						{
							name: "department",
							type: "text",
							required: false,
							admin: {
								description:
									"Department of the Job. This is automatically generated.",
							},
						},
						{
							name: "startDate",
							type: "text",
							required: false,
							admin: {
								description:
									"Start date of the Job. This is automatically generated.",
							},
						},
						{
							name: "timeLimitation",
							type: "text",
							required: false,
							admin: {
								description:
									"Time limitation of the Job. This is automatically generated.",
							},
						},
						{
							name: "externalLink",
							type: "text",
							required: false,
							admin: {
								description:
									"External link to the job. This is automatically generated.",
							},
						},
						{
							type: "collapsible",
							label: "Status",
							admin: { initCollapsed: false },
							fields: [
								{
									name: "lastSeen",
									type: "date",
									required: true,
									admin: {
										readOnly: true,
										description: "Last time the job was seen active",
									},
								},
							],
						},
					],
				},
				{
					label: "Internals",
					fields: [
						/**
						 * This is the raw data provided by the scraper.
						 */
						{
							name: "data",
							type: "json",
							required: true,
							admin: {
								readOnly: true,
								description: "Data provided for the job",
								disableListColumn: true,
							},
						},
						{
							name: "lastDataUpdate",
							type: "date",
							required: true,
							admin: {
								readOnly: true,
								description: "Last time the job data was updated",
								disableListColumn: true,
							},
						},
					],
				},
			],
		},
	],
	endpoints: [
		{
			path: "/sync",
			method: "post",
			handler: async (req) => {
				if (!req.user) {
					return Response.json({ error: "Unauthorized" }, { status: 401 });
				}

				await req.payload.jobs.queue({
					task: "sync-jobs",
					input: {},
				});

				return Response.json({});
			},
		},
	],
};
