import { getPlaiceholder } from "plaiceholder";

import { createCollectionAccessControl } from "@/payload/utils/access-control";

import type { CollectionConfig } from "payload";

export const mediaCollection: CollectionConfig = {
	slug: "media",
	admin: { useAsTitle: "name" },
	folders: true,
	access: {
		...createCollectionAccessControl(),
		read: () => true,
	},
	fields: [
		{
			name: "name",
			type: "text",
			required: false,
			localized: false,
			admin: {
				description:
					"Name of the media file. Will be overridden by the file name on upload.",
			},
		},
		{
			name: "alt",
			type: "text",
			required: false,
			localized: true,
			admin: {
				description: "Alt text for the image (for SEO)",
			},
		},
		{
			name: "blurhash",
			type: "text",
			required: true,
			admin: {
				hidden: true,
				disableListColumn: true,
			},
		},
	],
	hooks: {
		beforeOperation: [
			({ req, operation }) => {
				if ((operation === "create" || operation === "update") && req.file) {
					// Save the original file name to the request object.
					(req as any)._fileName = req.file.name;

					// Replace the file name with the generated ID.
					req.file.name = crypto.randomUUID();
				}
			},
		],
		beforeChange: [
			// This hook is used to generate the blurhash before the file is uploaded.
			async ({ req, data, operation }) => {
				// If the file is not empty, we need to generate the blurhash.
				// This applies for create and update operations.
				if (req.file) {
					let blurHash = "0";

					// This might fail if the uploaded file is not an image.
					try {
						blurHash = (
							await getPlaiceholder(req.file.data, {
								removeAlpha: false,
							})
						).base64;
					} catch (_) {}

					// Only the base64 property is required, which is a string.
					data.blurhash = blurHash;

					// Set the name field on creation only.
					if (operation === "create") {
						data.name = (req as any)._fileName || req.file.name;
					}
				}
			},
		],
	},
	upload: {
		bulkUpload: true,
		mimeTypes: ["image/*", "application/pdf"],
		disableLocalStorage: true,
		adminThumbnail: "thumbnail",
		focalPoint: true,
		crop: true,
		imageSizes: [
			// This additional size is only used for the admin thumbnail.
			{
				name: "thumbnail",
				width: 400,
				height: 400,
				position: "centre",
			},
		],
	},
};
