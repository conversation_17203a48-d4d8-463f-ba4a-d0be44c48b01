import { footerBlock } from "@/payload/blocks/special/footer-block";
import { headerBlock } from "@/payload/blocks/special/header-block";
import { sectionWrapperBlock } from "@/payload/blocks/special/section-wrapper-block";
import { createCollectionAccessControl } from "@/payload/utils/access-control";

import type { CollectionConfig } from "payload";

export const globalBlockCollection: CollectionConfig<"global-block"> = {
	slug: "global-block",
	admin: {
		useAsTitle: "title",
	},
	access: createCollectionAccessControl(),
	versions: { drafts: true },
	fields: [
		{
			name: "title",
			type: "text",
			required: true,
			admin: {
				description: "Title of the global block. Used for internal naming only",
			},
		},
		{
			name: "block",
			type: "blocks",
			required: true,
			maxRows: 1,
			minRows: 1,
			blocks: [headerBlock, footerBlock, sectionWrapperBlock],
			admin: {
				description: "Global block to display. Must be exactly one.",
			},
		},
	],
};
