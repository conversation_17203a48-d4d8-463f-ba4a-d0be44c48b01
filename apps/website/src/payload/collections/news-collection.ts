import { slugField } from "@/payload/fields/slug-field";
import { createCollectionAccessControl } from "@/payload/utils/access-control";

import type { CollectionConfig } from "payload";

export const newsCollection: CollectionConfig = {
	slug: "news",
	versions: { drafts: true },
	admin: {
		useAsTitle: "title",
		defaultColumns: ["title", "slug"],
	},
	access: createCollectionAccessControl(),
	fields: [
		{
			type: "tabs",
			tabs: [
				{
					label: "General",
					fields: [
						slugField(
							{ trackingFields: ["title"] },
							{
								required: true,
								unique: true,
								localized: true,
								index: true,
								admin: {
									description: `The URL slug of the news. This must only contain alphanumeric characters and dashes.`,
								},
							},
						),
						{
							name: "publishDate",
							type: "date",
							required: true,
							admin: {
								description: `The date the news was published.`,
							},
						},
						{
							name: "category",
							type: "select",
							required: true,
							options: [
								{ label: "Press Release", value: "press-release" },
								{ label: "Other", value: "other" },
							],
							admin: {
								description: `The category of the news.`,
							},
						},
						{
							name: "title",
							type: "text",
							required: true,
							admin: {
								description: `The title of the news.`,
							},
						},
						{
							name: "body",
							type: "richText",
							required: true,
							localized: true,
							admin: {
								disableListColumn: true,
							},
						},
					],
				},

				{
					label: "Images",
					fields: [
						{
							name: "coverImage",
							type: "upload",
							required: true,
							relationTo: "media",
							filterOptions: {
								mimeType: {
									contains: "image",
								},
							},
							admin: {
								disableListColumn: true,
							},
						},
					],
				},
			],
		},
	],
};
