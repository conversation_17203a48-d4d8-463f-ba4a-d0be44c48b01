import { deepmergeInto } from "deepmerge-ts";

import type { Field } from "payload";

type SlugField = (
	options?: { trackingFields?: string[] },
	overrides?: Partial<Field>,
) => Field;

export const slugField: SlugField = (
	{ trackingFields = ["title"] } = {},
	overrides,
) => {
	const field: Field = {
		name: "slug",
		unique: true,
		type: "text",
		admin: {
			components: {
				Field: {
					path: "@/payload/admin-components/SlugInput#SlugInput",
					clientProps: { trackingFields },
				},
			},
		},
		validate: (value: any) => {
			const defaultMessage = "Slug is invalid";
			if (typeof value !== "string") {
				return defaultMessage;
			}

			const regexp = /^[A-Za-z0-9-]+$/;
			if (!regexp.test(value)) {
				return defaultMessage;
			}

			return true;
		},
	};

	if (overrides) {
		deepmergeInto(field, overrides);
	}

	return field;
};
