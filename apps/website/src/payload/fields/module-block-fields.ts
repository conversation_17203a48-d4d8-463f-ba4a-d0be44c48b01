import type { Field } from "payload";

/**
 * Creates the fields for a module block.
 *
 * @param fields
 */
export const createModuleBlockFields = (fields: Field[]): Field[] => {
	return [
		{
			type: "tabs",
			tabs: [
				{
					label: "General",
					fields: [...fields],
				},
				{
					name: "background",
					label: "Background",
					interfaceName: "ModuleBlockBackground",
					fields: [
						{
							type: "select",
							name: "type",
							required: true,
							defaultValue: "default",
							options: [
								{ value: "default", label: "Default" },
								{ value: "gradient", label: "Gradient" },
							],
						},
					],
				},
			],
		},
	];
};
