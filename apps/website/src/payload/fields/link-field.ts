import { relationship, select, text } from "payload/shared";

import type {
	Field,
	PolymorphicRelationshipField,
	SelectField,
	TextField,
} from "payload";

interface LinkFieldProps {
	/**
	 * The type of the link.
	 */
	required?: boolean;

	/**
	 * If a label should be shown in the admin panel.
	 * Defaults to `false`.
	 */
	withLabel?: boolean;
}

export const linkField = (props: LinkFieldProps): Field => {
	const isRequired = props.required ?? true;
	const withLabel = props.withLabel ?? false;

	return {
		name: "link",
		interfaceName: "LinkField",
		type: "group",
		admin: {
			hideGutter: true,
		},
		fields: [
			{
				name: "label",
				type: "text",
				required: withLabel && isRequired,
				hidden: !withLabel,
				label: {
					en: "Label",
					de: "Bezeichnung",
				},
			} satisfies TextField,
			{
				fields: [
					{
						name: "type",
						admin: { layout: "horizontal", width: "50%" },
						defaultValue: isRequired ? "reference" : "none",
						options: [
							...(!isRequired
								? [{ label: { en: "None", de: "Keine" }, value: "none" }]
								: []),
							{
								label: { en: "Internal link", de: "Interner Link" },
								value: "reference",
							},
							{
								label: { en: "Custom", de: "Eigene URL" },
								value: "custom",
							},
							{
								label: { en: "Same Page", de: "Selbe Seite" },
								value: "same-page",
							},
							{
								label: { en: "Action", de: "Aktion" },
								value: "action",
							},
						],
						type: "radio",
					},
					{
						name: "newTab",
						admin: {
							style: { alignSelf: "flex-end" },
							width: "50%",
						},
						label: {
							en: "Open in new tab",
							de: "In neuem Tab öffnen",
						},
						type: "checkbox",
					},
				],
				type: "row",
			},
			{
				name: "reference",
				type: "relationship",
				admin: {
					condition: (_: any, siblingData: any) =>
						siblingData?.type === "reference",
				},
				label: {
					en: "Document to link to",
					de: "Verlinktes Dokument",
				},
				maxDepth: 1,
				relationTo: ["pages", "projects"],
				hasMany: false,
				required: false,
				validate: (value, ctx) => {
					if ((ctx.siblingData as any)?.type === "reference") {
						if (!value) {
							return ctx.req.t("validation:required");
						}
					}

					return relationship(value, ctx);
				},
			} satisfies PolymorphicRelationshipField,
			{
				name: "url",
				label: {
					en: "Custom URL",
					de: "Eigene URL",
				},
				admin: {
					condition: (_: any, siblingData: any) =>
						siblingData?.type === "custom",
					description: {
						en: "Custom URL to link to.",
						de: "Geben Sie eine eigene URL ein",
					},
				},
				required: false,
				validate: (value, ctx) => {
					if ((ctx.siblingData as any)?.type === "custom") {
						if (!value) {
							return ctx.req.t("validation:required");
						}
					}

					return text(value, ctx);
				},
				type: "text",
			} satisfies TextField,
			{
				name: "email",
				type: "text",
				label: {
					en: "Email address",
					de: "E-Mail-Adresse",
				},
				admin: {
					condition: (_: any, siblingData: any) =>
						siblingData?.type === "email",
				},
				required: false,
				validate: (value, ctx) => {
					if ((ctx.siblingData as any)?.type === "email") {
						if (!value) {
							return ctx.req.t("validation:required");
						}
					}

					return text(value, ctx);
				},
			} satisfies TextField,
			{
				name: "samePageIdentifier",
				type: "text",
				label: {
					en: "Section Identifier",
					de: "Sektionsbezeichner",
				},
				admin: {
					condition: (_: any, siblingData: any) =>
						siblingData?.type === "same-page",
				},
				required: false,
				validate: (value, ctx) => {
					if ((ctx.siblingData as any)?.type === "samePage") {
						if (!value) {
							return ctx.req.t("validation:required");
						}
					}

					return text(value, ctx);
				},
			} satisfies TextField,
			{
				name: "action",
				type: "select",
				label: {
					en: "Action",
					de: "Aktion",
				},
				options: [{ label: "Cookiebot Renew", value: "cookiebot-renew" }],
				admin: {
					condition: (_: any, siblingData: any) =>
						siblingData?.type === "action",
				},
				required: false,
				validate: (value, ctx) => {
					if ((ctx.siblingData as any)?.type === "action") {
						if (!value) {
							return ctx.req.t("validation:required");
						}
					}

					return select(value, ctx);
				},
			} satisfies SelectField,
		],
	} satisfies Field;
};
