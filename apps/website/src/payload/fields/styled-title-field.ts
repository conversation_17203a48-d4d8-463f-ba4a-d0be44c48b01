import {
	BoldFeature,
	ItalicFeature,
	lexicalEditor,
} from "@payloadcms/richtext-lexical";

import type { Field, RichTextField } from "payload";

type StyledTitleFieldArgs = Omit<RichTextField, "editor" | "type">;

/**
 * Creates a field for a title, which can be styled using the rich text editor.
 */
export const styledTitleField = (args: StyledTitleFieldArgs): Field => ({
	type: "richText",
	...args,
	editor: lexicalEditor({
		features: () => [BoldFeature(), ItalicFeature()],
	}),
	admin: {
		...args.admin,
		description: "Only the first line will be used.",
	},
});
