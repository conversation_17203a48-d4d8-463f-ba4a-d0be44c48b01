import { globalReferenceBlock } from "@/payload/blocks/utils/global-reference-block";

import type { GlobalBlock, GlobalReferenceBlock } from "@/gen/payload/types";
import type { BlocksField, Field } from "payload";

type CreateGlobalBlockReferenceFieldArgs = {
	allowedBlocks?: string[];
} & Omit<BlocksField, "maxRows" | "minRows" | "blocks" | "type">;

/**
 * Creates a field for referencing a global block of specific types.
 */
export const createGlobalBlockReferenceField = ({
	allowedBlocks,
	...fieldArgs
}: CreateGlobalBlockReferenceFieldArgs): Field => {
	return {
		...fieldArgs,
		type: "blocks",
		required: true,
		maxRows: 1,
		minRows: 1,
		blocks: [globalReferenceBlock],
		admin: {
			...fieldArgs.admin,
			description: `Reference to a global block. Must be exactly one. Allowed blocks: ${allowedBlocks?.join(", ")}.`,
		},
		validate: async (value, ctx) => {
			// Element must be set.
			if (!value || !Array.isArray(value) || value.length === 0) {
				return ctx.req.t("validation:required");
			}

			const block = value[0] as GlobalReferenceBlock;

			// Element must be a global reference.
			if (block.blockType !== globalReferenceBlock.slug) {
				return "Must be a global reference";
			}

			// Element must reference a global block.
			if (
				!block.reference ||
				block.reference.relationTo !== "global-block" ||
				!block.reference.value
			) {
				return "Must reference a global block";
			}

			// Load the global block for validation.
			let globalBlock: GlobalBlock;
			if (typeof block.reference.value === "string") {
				globalBlock = await ctx.req.payload.findByID({
					collection: "global-block",
					id: block.reference.value,
				});
			} else {
				globalBlock = block.reference.value;
			}

			// Global block must be found.
			if (!globalBlock) {
				return "Global block not found";
			}

			// Global block must contain a block.
			if (globalBlock.block.length === 0) {
				return "Global Block is empty";
			}

			// Check if the block is allowed.
			if (allowedBlocks) {
				if (!allowedBlocks.includes(globalBlock.block[0].blockType)) {
					return `Block of type "${globalBlock.block[0].blockType}" is not allowed`;
				}
			}

			return true;
		},
	};
};
