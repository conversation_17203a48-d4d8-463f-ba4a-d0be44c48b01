import { text } from "payload/shared";

import type { TextField } from "payload";

type CreateCollectionStringFieldArgs = Omit<
	TextField & { hasMany: false },
	"type" | "hasMany"
>;

/**
 * Creates a field, which lets the user select a single collection.
 * There is no select option for the user, only an input with validation.
 *
 * @param args
 */
export const createCollectionStringField = (
	args: CreateCollectionStringFieldArgs,
): TextField => ({
	validate: async (value, opts) => {
		// If the callee provided a custom validate function, call it first.
		if (args.validate) {
			const argsValidate = await args.validate?.(value, opts);
			if (argsValidate !== true) {
				return argsValidate;
			}
		}

		// Call the internal validation first for "required", etc.
		const upValidate = await text(value, opts);
		if (upValidate !== true) {
			return upValidate;
		}

		// If the value is not a string, we can't do any kind of validation.
		// The required validation is already done by the internal validation.
		if (typeof value !== "string" || value.length === 0) {
			return true;
		}

		const matchingCollection = opts.req.payload.config.collections.find(
			(c) => c.slug === value,
		);

		if (!matchingCollection) {
			return "Collection does not exist";
		}

		return true;
	},
	...args,
	type: "text",
	hasMany: false,
});
