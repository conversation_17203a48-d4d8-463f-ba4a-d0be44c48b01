import { text, textarea } from "payload/shared";

import { footerBlock } from "@/payload/blocks/special/footer-block";
import { headerBlock } from "@/payload/blocks/special/header-block";
import { sectionWrapperBlock } from "@/payload/blocks/special/section-wrapper-block";
import { sicoreMigrationModalBlock } from "@/payload/blocks/special/sicore-migration-modal-block";
import { globalReferenceBlock } from "@/payload/blocks/utils/global-reference-block";
import { createGlobalBlockReferenceField } from "@/payload/fields/global-block-reference-field";
import { analyzeStringTemplate } from "@/utils/string-templates";

import type {
	CollectionSlug,
	Tab,
	TextareaField,
	TextField,
	Validate,
} from "payload";

interface PageShellMetaTabArgs {
	referencingCollection?: CollectionSlug;
}

const createSubstitutionValidation = (
	collectionName: CollectionSlug | undefined,
): Validate<string | string[] | null | undefined, unknown, unknown> => {
	return (value, ctx) => {
		// If the value is not a string we can't do any kind of validation.l
		if (collectionName === undefined || typeof value !== "string") {
			return true;
		}

		const usedFields = analyzeStringTemplate(value);

		const collection = ctx.req.payload.collections[collectionName];

		// Collection must be found.
		if (!collection) {
			return "Collection not found";
		}

		const missingFields: string[] = [];
		const collectionFields = collection.config.flattenedFields;

		// Check if all fields are available.
		for (let field of usedFields) {
			if (!collectionFields.find((ff) => ff.name === field)) {
				missingFields.push(field);
			}
		}

		if (missingFields.length > 0) {
			return `Missing fields: ${missingFields.join(", ")}`;
		}

		return true;
	};
};

/**
 * Creates the fields for the layout tab of a page shell.
 */
export const createPageShellLayoutTab = (): Tab => {
	return {
		name: "layout",
		label: "Layout",
		interfaceName: "PageShellLayout",
		fields: [
			{
				name: "color",
				type: "select",
				options: ["meadow", "sky"],
				required: true,
				defaultValue: "meadow",
				admin: {
					description: `Highlight color of the page. Used for various elements as highlight color.`,
				},
			},
			createGlobalBlockReferenceField({
				name: "header",
				allowedBlocks: [headerBlock.slug],
			}),
			{
				name: "sections",
				type: "blocks",
				required: true,
				minRows: 1,
				blocks: [globalReferenceBlock, sectionWrapperBlock],
				admin: {
					description: `Sections of the page. Each section is a logical grouping of content. Must be at least one.`,
				},
			},
			createGlobalBlockReferenceField({
				name: "footer",
				allowedBlocks: [footerBlock.slug],
			}),
			{
				name: "floating",
				type: "blocks",
				required: false,
				minRows: 0,
				blocks: [globalReferenceBlock, sicoreMigrationModalBlock],
			},
		],
	};
};

export const createPageShellMetaTab = (args?: PageShellMetaTabArgs): Tab => {
	let descriptionSuffix = "";
	if (args?.referencingCollection) {
		descriptionSuffix = ` Substitutions are supported for collection "${args?.referencingCollection}"`;
	}

	return {
		name: "meta",
		label: "Meta",
		interfaceName: "PageShellMeta",
		fields: [
			{
				name: "title",
				type: "text",
				required: true,
				localized: true,
				admin: {
					description: `Meta Title of the page. Should be between 50-60 characters.${descriptionSuffix}.`,
				},
				validate: async (value, ctx) => {
					const defaultValidation = await text(value, ctx);
					if (defaultValidation !== true) {
						return defaultValidation;
					}

					// noinspection ES6RedundantAwait
					return await createSubstitutionValidation(
						args?.referencingCollection,
					)(value, ctx);
				},
			} satisfies TextField,
			{
				name: "description",
				type: "textarea",
				required: false,
				localized: true,
				admin: {
					description: `Meta Description of the page. Should be between 100-160 characters.${descriptionSuffix}`,
				},
				validate: async (value, ctx) => {
					const defaultValidation = await textarea(value, ctx);
					if (defaultValidation !== true) {
						return defaultValidation;
					}

					// noinspection ES6RedundantAwait
					return await createSubstitutionValidation(
						args?.referencingCollection,
					)(value, ctx);
				},
			} satisfies TextareaField,
		],
	};
};
