import * as fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { describe, expect, it } from "vitest";

import { convertHtmlToLexical } from "./lexical-converter";

const getFixtureHtml = (name: string): string => {
	return fs.readFileSync(
		path.join(
			path.dirname(fileURLToPath(import.meta.url)),
			"__fixtures__",
			name,
		),
		"utf-8",
	);
};

describe("payload/utils/lexical-converter", () => {
	describe("convertHtmlToLexical", () => {
		it("should convert HTML to Lexical", async () => {
			const input = `<p>Hello, world!</p>`;
			const output = await convertHtmlToLexical(input);

			expect(output).toMatchObject({
				root: {
					children: [
						{
							type: "paragraph",
							children: [{ text: "Hello, world!", type: "text" }],
						},
					],
					type: "root",
				},
			});
		});

		it("should convert job-v2 example to Lexical", async () => {
			const input = getFixtureHtml("job-v2.html");
			const output = await convertHtmlToLexical(input);

			expect(output).toMatchObject({
				root: {
					children: [
						{
							type: "paragraph",
						},
						{
							type: "list",
							children: [{ type: "listitem" }, { type: "listitem" }],
						},
						{
							type: "paragraph",
						},
						{
							type: "list",
							children: [{ type: "listitem" }, { type: "listitem" }],
						},
						{
							type: "paragraph",
						},
						{
							type: "list",
							children: [{ type: "listitem" }, { type: "listitem" }],
						},
					],
					type: "root",
				},
			});
		});
	});
});
