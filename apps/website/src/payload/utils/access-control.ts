import type { CollectionConfig } from "payload";

export const createCollectionAccessControl = (): CollectionConfig["access"] => {
	return {
		read: ({ req: { user } }) => {
			if (!user) {
				return false;
			}

			return true;
		},
		create: ({ req: { user } }) => {
			if (!user) {
				return false;
			}

			return user.role === "admin";
		},
		update: ({ req: { user } }) => {
			if (!user) {
				return false;
			}

			return user.role === "admin";
		},
		delete: ({ req: { user } }) => {
			if (!user) {
				return false;
			}

			return user.role === "admin";
		},
	};
};
