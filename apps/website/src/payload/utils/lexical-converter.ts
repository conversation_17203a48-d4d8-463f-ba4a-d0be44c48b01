import config from "@payload-config";
import {
	defaultEditorFeatures,
	getEnabledNodes,
	sanitizeServerEditorConfig,
} from "@payloadcms/richtext-lexical";
import { $getRoot, $getSelection } from "@payloadcms/richtext-lexical/lexical";
import { createHeadlessEditor } from "@payloadcms/richtext-lexical/lexical/headless";
import { $generateNodesFromDOM } from "@payloadcms/richtext-lexical/lexical/html";
import { JSDOM } from "jsdom";

import type { SerializedEditorState } from "@payloadcms/richtext-lexical/lexical";

/**
 * Converts the given HTML string to a Lexical editor state.
 *
 * @param input The HTML string to convert.
 * @returns The Lexical editor state.
 */
export const convertHtmlToLexical = async (
	input: string,
): Promise<SerializedEditorState> => {
	const headlessEditor = createHeadlessEditor({
		nodes: getEnabledNodes({
			editorConfig: await sanitizeServerEditorConfig(
				{ features: [...defaultEditorFeatures] },
				await config,
			),
		}),
	});

	headlessEditor.update(
		() => {
			const dom = new JSDOM(input);

			// Generate the Lexical nodes from the DOM.
			const nodes = $generateNodesFromDOM(headlessEditor, dom.window.document);

			// Select the root
			$getRoot().select();

			// Insert them at a selection.
			const selection = $getSelection();
			selection?.insertNodes(nodes);
		},
		{ discrete: true },
	);

	return headlessEditor.getEditorState().toJSON();
};
