import { NextResponse } from "next/server";
import createNextIntlMiddleware from "next-intl/middleware";

import { routing } from "@/i18n/routing";

import type { NextMiddleware } from "next/server";

const middleware: NextMiddleware = async (request) => {
	// This is a temporary fix to redirect the '/en' root path to the working '/de' path.
	if (request.nextUrl.pathname === "/en") {
		return NextResponse.redirect(new URL("/de", request.url), { status: 307 });
	}

	// This is the default (production) behavior for i18n.
	return createNextIntlMiddleware(routing)(request);
};

export const config = {
	// Match only internationalized pathnames
	matcher: [
		"/((?!api|admin|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)",
	],
};

export default middleware;
