import { cacheTag } from "next/dist/server/use-cache/cache-tag";
import { headers } from "next/headers";
import { notFound } from "next/navigation";
import { getPayload } from "payload";

import { F<PERSON><PERSON><PERSON>enderer } from "@/components/FBlockRenderer";
import { FPayloadLivePreview } from "@/components/FPayloadLivePreview";
import { defaultLocale } from "@/i18n";
import { isAvailableLocale } from "@/i18n/helper";
import payloadConfig from "@/payload/config";
import { buildPayloadPageCacheTags } from "@/utils/cache";
import { adjustLegacyData } from "@/utils/legacy";
import { remapObjectValues } from "@/utils/mapping";
import {
	buildExecutionsForBlocks,
	buildExecutionsForDocuments,
	runDataResolver,
	runDataResolversOnContext,
} from "@/utils/payload-block-data-resolver";
import { initPayloadDocument } from "@/utils/payload-block-utils";
import { matchPayloadCollectionMappings } from "@/utils/payload-collections-mapping";
import { getResolvedCollectionMappings } from "@/utils/payload-collections-mapping/direct";
import { initPayloadCollectionMappingsOnContext } from "@/utils/payload-collections-mapping/init";
import { isPayloadDocumentType } from "@/utils/payload-document/check";
import { extendPayloadDocument } from "@/utils/payload-document/extend";
import { lookupPayloadDocumentSlugs } from "@/utils/payload-link-lookup";
import { initPayloadLinkLookupFacade } from "@/utils/payload-link-lookup/init";
import { renderStringTemplate } from "@/utils/string-templates";
import { buildAbsoluteUrl, formatUrl } from "@/utils/url";

import type { PageShellMeta } from "@/gen/payload/types";
import type { AvailableLocale } from "@/i18n";
import type { PayloadBlockContext } from "@/utils/payload-block";
import type { PayloadBlockDataResolversResult } from "@/utils/payload-block-data-resolver/types";
import type { PayloadDocumentExtended } from "@/utils/payload-document";
import type { Metadata } from "next";
import type { CollectionSlug } from "payload";
import type { FC } from "react";

interface Params {
	slug: string[] | undefined;
	locale: string;
}

interface Props {
	params: Promise<Params>;
	searchParams: Promise<Record<string, any>>;
}

const PREVIEW_MODE_PREFIX = "-preview";

/**
 * Defines if the preview mode is active based on the given params.
 *
 * @param params The params for the current page.
 */
const isPreviewMode = (params: Params): boolean => {
	const slug = params.slug;

	if (slug === undefined || slug === null) {
		return false;
	}

	if (slug.length === 0) {
		return false;
	}

	return slug[0] === PREVIEW_MODE_PREFIX;
};

/**
 * Maps the incoming locale to a payload-compatible locale.
 *
 * @param params The params for the current page.
 */
const mapToPayloadLocale = (params: Params): AvailableLocale => {
	if (isAvailableLocale(params.locale)) {
		return params.locale;
	}

	return defaultLocale;
};

/**
 * For the given context, this loads the page itself.
 * This function is pure, meaning it only provides the data.
 *
 * @param ctx The context for the current page.
 */
const fetchDocumentForContext = async (
	ctx: PayloadBlockContext,
): Promise<PayloadDocumentExtended | undefined> => {
	const payload = await getPayload({ config: payloadConfig });

	const filterCollection = ctx.document.collection as CollectionSlug;
	let filterField = ctx.document.identifier.field;
	let filterValue = ctx.document.identifier.value;

	if (filterCollection === "pages" && filterField === "slug") {
		filterValue = "/" + filterValue;
	}

	// Check if the collection has versioning.
	// This is necessary for additional conditions.
	const hasVersioning =
		!!payload.collections[filterCollection].config.versions.drafts;

	const documentResponse = await payload.find({
		collection: filterCollection,
		locale: ctx.locale,
		where: {
			[filterField]: { equals: filterValue },

			// If we are not in preview mode, we only include published pages.
			...(hasVersioning &&
				!ctx.isPreview && {
					_status: { equals: "published" },
				}),
		},

		// If this is intended for preview mode, we include draft versions.
		draft: ctx.isPreview,

		// Use a depth of 100 to ensure that all pages are included.
		depth: 100,
	});

	if (!documentResponse.docs?.length) {
		return undefined;
	}

	return extendPayloadDocument(adjustLegacyData(documentResponse.docs[0]), {
		collection: filterCollection as CollectionSlug,
	});
};

/**
 * Cached version of the {@link fetchDocumentForContext} function.
 * This function is used to cache the page for the current context.
 * The context will be used as the cache key.
 */
const fetchDocumentForContextCached = async (
	ctx: PayloadBlockContext,
): Promise<any | undefined> => {
	"use cache";

	// The cache tags for the current page.
	cacheTag(
		...buildPayloadPageCacheTags({ path: ctx.path, locale: ctx.locale }),
	);

	return await fetchDocumentForContext(ctx);
};

/**
 * Builds the context for the current page.
 *
 * @param params The params for the current page.
 * @param searchParams The search params for the current page.
 */
const resolveContext = async (
	params: Params,
	searchParams: Record<string, any>,
): Promise<PayloadBlockContext> => {
	const isPreview = isPreviewMode(params);

	const mappings = await getResolvedCollectionMappings();
	const inferredLocale = mapToPayloadLocale(params);
	let inferredPath = "/" + (params.slug?.join("/") ?? "");
	if (isPreview) {
		inferredPath = "/" + (params.slug?.slice(1)?.join("/") ?? "");
	}

	// Try to match the path against the collection mappings.
	const matched = await matchPayloadCollectionMappings({
		path: inferredPath,
		locale: inferredLocale,
		mappings,
	});

	// If no match is found, we return a 404.
	if (!matched) {
		notFound();
	}

	if (isPreview) {
		const payload = await getPayload({ config: payloadConfig });

		const authed = await payload.auth({
			headers: await headers(),
		});

		if (!authed.user) {
			return notFound();
		}
	}
	// Build the basic context.
	const ctx = {
		document: {
			collection: matched.mapping.collection,
			identifier: matched.identifier,
		},
		path: inferredPath,
		locale: inferredLocale,
		searchParams: searchParams,
		baseUrl: buildAbsoluteUrl("").toString(),
		isPreview,
	} satisfies PayloadBlockContext;

	await initPayloadLinkLookupFacade(ctx);
	await initPayloadCollectionMappingsOnContext(ctx);

	return ctx;
};

/**
 * Resolves the page shell meta data for the given document and resolved data.
 *
 * @param document
 * @param resolvedData
 */
const resolvePageShellMeta = (
	document: PayloadDocumentExtended,
	resolvedData: PayloadBlockDataResolversResult,
): PageShellMeta | undefined => {
	if (!document) {
		return undefined;
	}

	// If this is a page, we can directly return the meta data.
	if (isPayloadDocumentType(document, "pages")) {
		return document.meta;
	}

	const identity = `${document.$collectionName}-document-shell$${document.id}`;

	// The data resolvers of document shells must ONLY return the shell, not in a nested structure.
	return remapObjectValues(resolvedData[identity]?.meta, (_, value: string) =>
		renderStringTemplate(value, document, (it) => `#MISSING#${it}#`),
	);
};

const CatchAllPage: FC<Props> = async (props) => {
	// Resolve all the required props.
	const _params = await props.params;
	const _searchParams = await props.searchParams;

	// Build the context for the page.
	const ctx = await resolveContext(_params, _searchParams);

	// Fetch the page. If we're not in preview-mode, we can use the cached version.
	let documentData: any | undefined;
	if (!ctx.isPreview) {
		documentData = await fetchDocumentForContext(ctx);
	} else {
		documentData = await fetchDocumentForContextCached(ctx);
	}

	if (!documentData) {
		return notFound();
	}

	// Initialize the document data.
	initPayloadDocument(ctx, documentData);

	// Runs all the data resolvers for the current document.
	await runDataResolversOnContext(ctx, {
		...buildExecutionsForBlocks(documentData),
		...buildExecutionsForDocuments(ctx, documentData),
	});

	// Only render this special document shell component.
	// This will internally adjust the data to load the respective document shell.
	return (
		<>
			{ctx.isPreview && (
				<FPayloadLivePreview
					serverURL={formatUrl(buildAbsoluteUrl(), {
						removeTrailingSlash: true,
					})}
				/>
			)}

			<FBlockRenderer
				block={{
					blockType: `${ctx.document.collection}-document-shell`,
					...documentData,
				}}
				ctx={ctx}
			/>
		</>
	);
};

export const generateMetadata = async (props: Props): Promise<Metadata> => {
	const params = await props.params;
	const searchParams = await props.searchParams;

	// Build the context for the page.
	const ctx = await resolveContext(params, searchParams);

	// Fetch the page. If we're not in preview-mode, we can use the cached version.
	let documentData: any | undefined;
	if (!ctx.isPreview) {
		documentData = await fetchDocumentForContext(ctx);
	} else {
		documentData = await fetchDocumentForContextCached(ctx);
	}

	if (!documentData) {
		return notFound();
	}

	// Initialize the document data extension.
	initPayloadDocument(ctx, documentData);

	// Runs all the data resolvers for the current document.
	const dataResolvers = await runDataResolver(
		ctx,
		buildExecutionsForDocuments(ctx, documentData),
	);

	// Loads all the slugs for the current page.
	const slugs = lookupPayloadDocumentSlugs({
		ctx,
		pageId: documentData.id,
	});

	// If the slugs for the current page are available,
	// we can generate the alternative languages.
	const alternateLanguages: Record<string, string> = {};
	if (slugs) {
		for (const [locale, slug] of Object.entries(slugs)) {
			if (locale !== ctx.locale) {
				// Special case handling for the "/" slug.
				let targetSlug = slug;
				if (slug === "/") {
					targetSlug = "";
				}

				alternateLanguages[locale] = `/${locale}${targetSlug}`;
			}
		}
	}

	const pageShellMeta = resolvePageShellMeta(documentData, dataResolvers);

	return {
		metadataBase: buildAbsoluteUrl("/"),
		title: pageShellMeta?.title,
		description: pageShellMeta?.description,
		alternates: { languages: alternateLanguages },
	};
};

// Revalidate time in seconds.
// This will be used to revalidate the page after 10 minutes.
export const revalidate = 600;

// Always force the page to be dynamic.
export const dynamic = "force-dynamic";

export default CatchAllPage;
