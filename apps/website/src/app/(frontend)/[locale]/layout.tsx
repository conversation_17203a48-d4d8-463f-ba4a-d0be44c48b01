import clsx from "clsx";
import localFont from "next/font/local";
import Script from "next/script";

import "./global.css";
import { FRootWrapper } from "@/components/FRootWrapper";

import type { FC, PropsWithChildren } from "react";

type LayoutProps = PropsWithChildren & {
	params: Promise<{ locale: string }>;
};

const fontDinNextSlabPro = localFont({
	variable: "--font-din-next-slab",
	preload: true,
	src: [
		{
			path: "../../../assets/fonts/DINNextSlab/DINNextSlab-Light.woff2",
			weight: "400",
			style: "normal",
		},
		{
			path: "../../../assets/fonts/DINNextSlab/DINNextSlab-Medium.woff2",
			weight: "500",
			style: "normal",
		},
	],
});

const helveticaNeueLtStd = localFont({
	variable: "--font-helvetica-neue-lt",
	preload: true,
	src: [
		{
			path: "../../../assets/fonts/HelveticaNeueLTPro/HelveticaNeueLTPro-45Light.woff2",
			weight: "300",
			style: "normal",
		},
		{
			path: "../../../assets/fonts/HelveticaNeueLTPro/HelveticaNeueLTPro-75Bold.woff2",
			weight: "700",
			style: "normal",
		},
	],
});

const Layout: FC<LayoutProps> = async (props) => {
	const params = await props.params;

	const isDevMode = process.env.NODE_ENV === "development";
	const shouldLoadAnalytics = !isDevMode;

	// Cookiebot Culture defines the language of the cookie consent banner.
	const cbCulture = params.locale?.toUpperCase();

	return (
		<html
			suppressHydrationWarning
			lang={params.locale}
			className="h-full antialiased [font-synthesis-weight:none]"
		>
			<head>
				<Script
					id="cookiebot-init"
					src="https://consent.cookiebot.com/uc.js"
					data-cbid="104239c1-d73f-45e9-877c-0dd3b099ac61"
					data-blockingmode="auto"
					type="text/javascript"
					data-culture={cbCulture}
				/>
				<script
					dangerouslySetInnerHTML={{
						__html: `/* #Version: ${__CONFIG_BUILD_ID} */`,
					}}
				/>
			</head>
			<body
				className={clsx(
					fontDinNextSlabPro.variable,
					helveticaNeueLtStd.variable,
					"text-body font-body",
				)}
			>
				<FRootWrapper extras={{ locale: params.locale }}>
					{props.children}
				</FRootWrapper>

				{/* Use consent mode for Google Analytics */}
				{shouldLoadAnalytics && (
					<>
						{/* Use Google Consent Mode before loading 'gtag' */}
						<Script id="google-analytics-consent-mode">
							{`function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("consent","default",{ad_personalization:"denied",ad_storage:"denied",ad_user_data:"denied",analytics_storage:"denied",functionality_storage:"denied",personalization_storage:"denied",security_storage:"granted",wait_for_update:500}),gtag("set","ads_data_redaction",!0),gtag("set","url_passthrough",!0);`}
						</Script>

						<Script
							async
							src="https://www.googletagmanager.com/gtag/js?id=G-2W6EJ7YJR2"
						/>

						<Script id="google-analytics-setup">
							{`function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","G-2W6EJ7YJR2");`}
						</Script>
					</>
				)}
			</body>
		</html>
	);
};

export default Layout;
