@import "tailwindcss";

@plugin "@tailwindcss/typography";

html {
	/**
		Addresses an issue with headless-ui dialogs as they add padding-right to the html element.
		This causes an issue with the fixed position of the header, because the content shifts but the header is not.
	 */
	padding-right: 0 !important;
}


/* Fonts */
@theme inline {
	/*noinspection CssUnresolvedCustomProperty*/
	--font-body: var(--font-din-next-slab), var(--font-serif);
	/*noinspection CssUnresolvedCustomProperty*/
	--font-display: var(--font-helvetica-neue-lt), var(--font-sans);
}

/* Custom colors*/
@theme {
	--color-meadow-50: #F6F8F6;
	--color-meadow-100: #EBF0EB;
	--color-meadow-200: #D9E2D9;
	--color-meadow-300: #C5D3C5;
	--color-meadow-400: #B1C4B1;
	--color-meadow-500: #9DB59D;
	--color-meadow-600: #769876;
	--color-meadow-700: #587458;
	--color-meadow-800: #3B4E3B;
	--color-meadow-900: #1D261D;
	--color-meadow-950: #0F140F;
	--color-meadow: #9DB59D;

	--color-sky-50: #EFF3F6;
	--color-sky-100: #E1E9EF;
	--color-sky-200: #C1D0DC;
	--color-sky-300: #A3BACC;
	--color-sky-400: #86A4BC;
	--color-sky-500: #668CAA;
	--color-sky-600: #4E728D;
	--color-sky-700: #3A5569;
	--color-sky-800: #263845;
	--color-sky-900: #141D24;
	--color-sky-950: #090D10;
	--color-sky: #668CAA;

	--color-ocean-50: #D6EDFF;
	--color-ocean-100: #ADDCFF;
	--color-ocean-200: #57B6FF;
	--color-ocean-300: #0593FF;
	--color-ocean-400: #0062AD;
	--color-ocean-500: #003259;
	--color-ocean-600: #002847;
	--color-ocean-700: #002038;
	--color-ocean-800: #001424;
	--color-ocean-900: #000C14;
	--color-ocean-950: #00060A;
	--color-ocean: #003259;

	--color-beach-50: #FEFBF6;
	--color-beach-100: #FDF7ED;
	--color-beach-200: #FBECD6;
	--color-beach-300: #F9E4C3;
	--color-beach-400: #F6DAAC;
	--color-beach-500: #F4D198;
	--color-beach-600: #ECB050;
	--color-beach-700: #D98E17;
	--color-beach-800: #8F5E0F;
	--color-beach-900: #4A3008;
	--color-beach-950: #251804;

	--color-highlighted: var(--color-highlighted);
}

@theme {
	--container-8xl: 90rem;
}

@layer utilities {
	.text-title-hero {
		@apply text-4xl tracking-widest leading-[1.1];

		@variant min-md {
			@apply text-5xl;
		}

		@variant min-xl {
			@apply text-6xl;
		}
	}

	.text-title-section {
		@apply text-3xl tracking-widest leading-[1.1];

		@variant min-md {
			@apply text-4xl;
		}

		@variant min-xl {
			@apply text-5xl;
		}
	}

	.text-body-title {
		@apply text-lg tracking-normal leading-snug font-semibold;

		@variant min-md {
			@apply text-2xl;
		}

		@variant min-xl {
			@apply text-3xl;
		}
	}

	.text-body {
		@apply text-base tracking-normal leading-snug;

		@variant min-md {
			@apply text-xl;
		}

		@variant min-xl {
			@apply text-2xl;
		}
	}

	.text-body-smaller {
		@apply text-base tracking-normal leading-snug;

		@variant min-md {
			@apply text-base;
		}

		@variant min-xl {
			@apply text-lg;
		}
	}

	.prose-default {
		@apply text-balance text-black;
		@apply text-base tracking-normal leading-snug;
		@apply marker:text-highlighted marker:text-[1.5em];

		@apply prose-ul:m-0 prose-li:my-0;

		@variant min-md {
			@apply text-xl;
		}

		@variant min-xl {
			@apply text-2xl;
		}
	}
}

@layer utilities {
	.transition-config-default {
		@apply duration-200 ease-in-out;
	}
}

.my-gradient {
	background: /* White radial “spot” near bottom-left */ radial-gradient(
		circle at 20% 90%,
		#ffffff 0%,
		rgba(255, 255, 255, 0) 50%
	),
		/* Main linear gradient from blue to green */ linear-gradient(
		135deg,
		#9bd7ff 0%,
		#b7dfc5 100%
	);
}

.ham {
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition-timing-function: linear;
	transition-duration: .15s;
	transition-property: opacity;

	--ham-bar-width: 32px;
	--ham-bar-height: 4px;
	--ham-spacing: 4px;
	--ham-border-radius: 0px;

	width: var(--ham-bar-width);
	height: calc((var(--ham-bar-height) * 3) + (var(--ham-spacing) * 2));
}

.ham-box {
	position: relative;
	display: inline-block;

	width: var(--ham-bar-width);
	height: calc((var(--ham-bar-height) * 3) + (var(--ham-spacing) * 2));
}

.ham-active > .ham-box > .ham-inner {
	transition-delay: .12s;
	transition-timing-function: cubic-bezier(.215, .61, .355, 1);
	transform: rotate(45deg);
}

.ham-active > .ham-box > .ham-inner::before {
	top: 0;
	opacity: 0;
	transition: top 75ms ease, opacity 75ms ease .12s
}

.ham-active > .ham-box > .ham-inner::after {
	bottom: 0;
	transition: bottom 75ms ease, transform 75ms cubic-bezier(.215, .61, .355, 1) .12s;
	transform: rotate(-90deg);
}

.ham-inner {
	top: 50%;
	display: block;
	background-color: #000;

	position: absolute;
	border-radius: var(--ham-border-radius);

	transition-timing-function: cubic-bezier(.55, .055, .675, .19);
	transition-duration: 75ms;
	transition-property: transform;

	margin-top: calc(var(--ham-bar-height) / -2);
	width: var(--ham-bar-width);
	height: var(--ham-bar-height);
}


.ham-inner::after,
.ham-inner::before {
	content: "";
	background-color: inherit;

	position: absolute;
	border-radius: var(--ham-border-radius);

	transition-timing-function: ease;
	transition-duration: .15s;
	transition-property: transform;

	width: var(--ham-bar-width);
	height: var(--ham-bar-height);
}

.ham-inner::before {
	top: calc((var(--ham-spacing) + var(--ham-bar-height)) * -1);
	transition: top 75ms ease .12s, opacity 75ms ease;
}

.ham-inner::after {
	bottom: calc((var(--ham-spacing) + var(--ham-bar-height)) * -1);
	transition: bottom 75ms ease .12s, transform 75ms cubic-bezier(.55, .055, .675, .19);
}