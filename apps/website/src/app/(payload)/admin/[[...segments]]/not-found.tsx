/* THIS FILE WAS GENERATED AUTOMATICALLY BY PAYLOAD. */
/* DO NOT MODIFY IT BECAUSE IT COULD BE REWRITTEN AT ANY TIME. */

import config from "@payload-config";
import { NotFoundPage, generatePageMetadata } from "@payloadcms/next/views";

import { importMap } from "../importMap";

import type { Metadata } from "next";

interface Args {
	params: Promise<{
		segments: string[];
	}>;
	searchParams: Promise<{
		[key: string]: string | string[];
	}>;
}

const NotFound = ({ params, searchParams }: Args) =>
	NotFoundPage({ config, params, searchParams, importMap });

export const generateMetadata = ({
	params,
	searchParams,
}: Args): Promise<Metadata> =>
	generatePageMetadata({ config, params, searchParams });

export default NotFound;
