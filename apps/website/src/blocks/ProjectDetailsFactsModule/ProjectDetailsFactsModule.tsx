import { defineBlockComponent } from "@/blocks/utils";
import { CFactsList, CFactsListItem } from "@/components/CFactsList";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { getPayloadDocument } from "@/utils/payload-block-utils";

import type { ProjectDetailsFactsModuleBlock } from "@/gen/payload/types";

export const ProjectDetailsFactsModule =
	defineBlockComponent<ProjectDetailsFactsModuleBlock>(
		"project-details-facts-module",
		({ block, ctx }) => {
			const project = getPayloadDocument(ctx, "projects");

			return (
				<LModuleWrapper
					block={block}
					className="grid lg:grid-cols-2 gap-x-16 gap-y-12"
				>
					<CFactsList className="lg:col-start-2">
						{project.addressFull && (
							<CFactsListItem title={block.labelAddress}>
								{project.addressFull}
							</CFactsListItem>
						)}

						{project.facts?.map((fact, idx) => (
							<CFactsListItem key={idx} title={fact.key}>
								{fact.value}
							</CFactsListItem>
						))}
					</CFactsList>
				</LModuleWrapper>
			);
		},
		// Do not render if there are no facts.
		({ ctx }) =>
			(getPayloadDocument(ctx, "projects")?.facts?.length ?? 0) > 0 ||
			getPayloadDocument(ctx, "projects")?.addressFull !== undefined,
	);
