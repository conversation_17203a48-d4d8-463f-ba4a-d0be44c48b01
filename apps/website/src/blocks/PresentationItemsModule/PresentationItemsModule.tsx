import clsx from "clsx";
import { Fragment } from "react";

import { defineBlockComponent } from "@/blocks/utils";
import { F<PERSON><PERSON><PERSON>enderer } from "@/components/FBlockRenderer";
import { FImage } from "@/components/FImage";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";

import type { PresentationItemsModuleBlock } from "@/gen/payload/types";

export const PresentationItemsModule =
	defineBlockComponent<PresentationItemsModuleBlock>(
		"presentation-items-module",
		({ block, ctx }) => {
			return (
				<LModuleWrapper block={block}>
					<div
						className={clsx(
							"grid grid-flow-row lg:grid-rows-[auto_auto_auto] lg:grid-flow-col auto-cols-fr",
							"gap-x-16 gap-y-8",
						)}
					>
						{block.items?.map((item, idx) => (
							<Fragment key={idx}>
								<div
									className={clsx(
										"flex flex-col gap-y-8",
										idx > 0 && "pt-16 lg:pt-0",
									)}
								>
									{item.icon && (
										<div className="bg-highlighted flex items-center justify-center py-4 md:py-6 lg:py-8">
											<FImage
												className="h-28 md:h-36 lg:h-44 w-auto"
												media={item.icon as any}
											/>
										</div>
									)}

									<h2
										className={clsx(
											"text-body font-display font-bold",
											item.titleUppercase && "uppercase",
										)}
									>
										<FLexicalRenderer
											content={item.title}
											firstParagraphOnly={true}
										/>
									</h2>
								</div>

								<div className={clsx("flex flex-col gap-y-8")}>
									{item.body && (
										<div className="text-body">
											<FLexicalRenderer content={item.body} />
										</div>
									)}
								</div>

								<div>
									{item?.actions?.map((action) => (
										<FBlockRenderer key={action.id} block={action} ctx={ctx} />
									))}
								</div>
							</Fragment>
						))}
					</div>
				</LModuleWrapper>
			);
		},
	);
