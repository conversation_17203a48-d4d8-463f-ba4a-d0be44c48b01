import { defineBlockComponent } from "@/blocks/utils";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { getPayloadDocument } from "@/utils/payload-block-utils";

import type { ProjectDetailsHeroModuleBlock } from "@/gen/payload/types";

export const ProjectDetailsHeroModule =
	defineBlockComponent<ProjectDetailsHeroModuleBlock>(
		"project-details-hero-module",
		({ block, ctx }) => {
			const project = getPayloadDocument(ctx, "projects");

			return (
				<LModuleWrapper
					block={block}
					className="flex flex-col lg:flex-row lg:items-end gap-8"
				>
					<div className="flex-1">
						<h2 className="font-display uppercase text-title-section text-balance hyphens-auto">
							{project.title}
						</h2>
					</div>
				</LModuleWrapper>
			);
		},
	);
