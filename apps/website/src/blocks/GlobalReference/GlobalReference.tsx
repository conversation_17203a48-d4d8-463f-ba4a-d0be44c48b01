import { defineBlockComponent } from "@/blocks/utils";
import { F<PERSON><PERSON><PERSON>enderer } from "@/components/FBlockRenderer";

import type { GlobalReferenceBlock } from "@/gen/payload/types";

export const GlobalReference = defineBlockComponent<GlobalReferenceBlock>(
	"global-reference",
	({ block, ctx }) => {
		return (
			<FBlockRenderer
				block={(block.reference.value as any).block[0]}
				ctx={ctx}
			/>
		);
	},
);
