import clsx from "clsx";

import { defineBlockComponent } from "@/blocks/utils";
import { CSectionSeparator } from "@/components/CSectionSeparator";
import { FBlockRenderer } from "@/components/FBlockRenderer";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";

import type { IntroModuleBlock } from "@/gen/payload/types";

export const IntroModule = defineBlockComponent<IntroModuleBlock>(
	"intro-module",
	({ block, ctx }) => {
		return (
			<LModuleWrapper block={block}>
				<div className={clsx("relative flex flex-col gap-y-12")}>
					<div className="flex flex-col">
						<h1 className="font-display text-title-section uppercase">
							<FLexicalRenderer content={block.title} />
						</h1>

						{block.withDivider && (
							<div className="w-52 mt-12">
								<CSectionSeparator />
							</div>
						)}
					</div>

					<div className="grid lg:grid-cols-2 gap-x-16 gap-y-8">
						<div className="prose prose-default max-w-full lg:col-start-2">
							{block.body && <FLexicalRenderer content={block.body} />}
						</div>

						{block.actions && block.actions.length > 0 && (
							<div
								className={clsx(
									"flex items-end",
									block.actionsPlacement === "below-body" && "lg:col-start-2",
									block.actionsPlacement === "standalone" &&
										"lg:col-start-1 lg:row-start-1",
								)}
							>
								<div>
									{block.actions.map((action, idx) => (
										<FBlockRenderer block={action} key={idx} ctx={ctx} />
									))}
								</div>
							</div>
						)}
					</div>
				</div>
			</LModuleWrapper>
		);
	},
);
