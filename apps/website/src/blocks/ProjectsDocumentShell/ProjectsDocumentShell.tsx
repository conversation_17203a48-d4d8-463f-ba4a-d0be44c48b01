import { defineBlockComponent } from "@/blocks/utils";
import { LPageShellLayout } from "@/components/LPageShellLayout";

import type { ProjectsDocumentShellDataResolver } from "@/blocks/ProjectsDocumentShell/ProjectsDocumentShellDataResolver";
import type { DocumentShellBlock } from "@/blocks/utils/types";
import type { Project } from "@/gen/payload/types";

export const ProjectsDocumentShell = defineBlockComponent<
	DocumentShellBlock<Project, "projects">,
	typeof ProjectsDocumentShellDataResolver
>("projects-document-shell", ({ ctx, data }) => (
	<LPageShellLayout data={data.layout} ctx={ctx} />
));
