import payloadConfig from "@payload-config";
import { getPayload } from "payload";

import { defineBlockDataResolver } from "@/blocks/utils/block-data-resolver";
import { adjustLegacyData } from "@/utils/legacy";

export const ProjectsDocumentShellDataResolver = defineBlockDataResolver(
	"projects-document-shell",
	async (ctx) => {
		const payload = await getPayload({ config: payloadConfig });

		const data = await payload.findGlobal({
			slug: "projects-shell",
			locale: ctx.locale,
		});

		return adjustLegacyData(data);
	},
);
