import { defineBlockComponent } from "@/blocks/utils";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";

import type { TypographyShowcaseModuleBlock } from "@/gen/payload/types";

export const TypographyShowcaseModule =
	defineBlockComponent<TypographyShowcaseModuleBlock>(
		"typography-showcase-module",
		({ block }) => {
			return (
				<LModuleWrapper block={block} size="small">
					<div className="prose prose-default">
						<FLexicalRenderer content={block.body} />
					</div>
				</LModuleWrapper>
			);
		},
	);
