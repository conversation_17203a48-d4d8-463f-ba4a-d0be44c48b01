import { defineBlockComponent } from "@/blocks/utils";
import { CButtonLink } from "@/components/CButton";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { getPayloadDocument } from "@/utils/payload-block-utils";

import type { JobDetailsBodyModuleBlock } from "@/gen/payload/types";

export const JobDetailsBodyModule =
	defineBlockComponent<JobDetailsBodyModuleBlock>(
		"job-details-body-module",
		({ block, ctx }) => {
			const job = getPayloadDocument(ctx, "jobs");

			return (
				<LModuleWrapper block={block} className="">
					<div className="md:columns-2 gap-16 prose prose-default max-w-full">
						{job.description && <FLexicalRenderer content={job.description} />}
					</div>

					{job.externalLink && (
						<div className="mt-16">
							<CButtonLink
								size="extended"
								target="_blank"
								allowMultiLine={true}
								href={job.externalLink!}
							>
								{block.labelApply}
							</CButtonLink>
						</div>
					)}
				</LModuleWrapper>
			);
		},
	);
