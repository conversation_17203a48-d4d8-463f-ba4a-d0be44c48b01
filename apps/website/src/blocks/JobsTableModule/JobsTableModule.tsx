import { JobsTable } from "@/blocks/JobsTableModule/components/Table";
import { defineBlockComponent } from "@/blocks/utils";
import { LModuleWrapper } from "@/components/LModuleWrapper";

import type { JobsTableModuleDataResolver } from "@/blocks/JobsTableModule/JobsTableModuleDataResolver";
import type { JobsTableModuleBlock } from "@/gen/payload/types";

export const JobsTableModule = defineBlockComponent<
	JobsTableModuleBlock,
	typeof JobsTableModuleDataResolver
>("jobs-table-module", async ({ block, ctx, data }) => {
	const sorting = {
		field: ctx.searchParams.sortField ?? undefined,
		direction: ctx.searchParams.sortDirection ?? undefined,
	};

	return (
		<LModuleWrapper block={block}>
			<JobsTable
				data={data}
				sorting={sorting}
				labels={{
					title: block.labelTitle,
					entryLevel: block.labelEntryLevel,
					employmentType: block.labelEmploymentType,
				}}
			/>
		</LModuleWrapper>
	);
});
