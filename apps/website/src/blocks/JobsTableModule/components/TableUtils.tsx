"use client";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import clsx from "clsx";

import type { FC, ReactNode } from "react";

interface JobsTableHeadColumnProps {
	children?: ReactNode;

	/**
	 * Direction of the sorting. Defines the arrow direction.
	 */
	direction?: "asc" | "desc" | undefined;

	/**
	 * Callback to change the sorting direction.
	 * If not provided, the sorting is not enabled.
	 */
	onChangeDirection?: (direction: "asc" | "desc" | undefined) => void;
}

interface JobsTableHeadProps {
	children?: ReactNode;
}

interface JobsTableRowProps {
	children?: ReactNode;
}

export const JobsTableHeadColumn: FC<JobsTableHeadColumnProps> = ({
	children,
	direction,
	onChangeDirection,
}) => {
	const supportsSorting = !!onChangeDirection;

	return (
		<th className={clsx("text-left uppercase px-2 first:pl-0 last:pr-0 pb-4")}>
			<div
				className={clsx(
					"group w-full font-normal bg-black text-white flex justify-between items-center px-2 gap-x-2",
					supportsSorting && "cursor-pointer",
				)}
				onClick={() => {
					if (!supportsSorting) {
						return;
					}

					let nextDirection: "asc" | "desc" | undefined;

					if (direction === undefined) {
						nextDirection = "asc";
					} else if (direction === "asc") {
						nextDirection = "desc";
					} else {
						nextDirection = undefined;
					}

					onChangeDirection(nextDirection);
				}}
			>
				<div className="py-2 leading-none truncate min-w-0">{children}</div>

				{supportsSorting && (
					<ChevronDownIcon
						className={clsx(
							"h-8 w-8",
							direction === undefined &&
								"opacity-0 group-hover:opacity-75 transition-opacity duration-200 ease-in-out",
							direction === "desc" && "rotate-180",
							direction === "asc" && "rotate-0",
						)}
					/>
				)}
			</div>
		</th>
	);
};

export const JobsTableHead: FC<JobsTableHeadProps> = ({ children }) => {
	return (
		<thead>
			<tr>{children}</tr>
		</thead>
	);
};

export const JobsTableRow: FC<JobsTableRowProps> = ({ children }) => {
	return (
		<tr
			className={clsx(
				"[&>td]:w-1/3 [&>td]:py-3 [&>td]:px-2 [&>td:first-child]:pl-0 [&>td:last-child]:pr-0 [&>td]:align-top",
				"border-b-black border-b",
			)}
		>
			{children}
		</tr>
	);
};
