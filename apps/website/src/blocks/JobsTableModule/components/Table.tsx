"use client";
import { useSearchParams } from "next/navigation";
import { useMemo } from "react";

import {
	JobsTableHead,
	JobsTableHeadColumn,
	JobsTableRow,
} from "@/blocks/JobsTableModule/components/TableUtils";
import { jobsTableSortFieldsAllowed } from "@/blocks/JobsTableModule/utils";
import { Link, usePathname, useRouter } from "@/navigation";

import type { JobsTableModuleDataResolver } from "@/blocks/JobsTableModule/JobsTableModuleDataResolver";
import type { InferDataResolverReturn } from "@/blocks/utils";
import type { FC } from "react";

interface Sorting {
	field: string;
	direction: "asc" | "desc";
}

interface Props {
	data: InferDataResolverReturn<typeof JobsTableModuleDataResolver>;
	sorting: Sorting | undefined;
	labels: {
		title: string;
		entryLevel: string;
		employmentType: string;
	};
}

const convertSearchParams = (
	searchParams: URLSearchParams,
): Record<string, string | string[]> => {
	const result: Record<string, string | string[]> = {};

	for (const [key, value] of searchParams.entries()) {
		result[key] = value;
	}

	return result;
};

export const JobsTable: FC<Props> = ({
	data,
	sorting: externalSorting,
	labels,
}) => {
	const router = useRouter();
	const pathname = usePathname();
	const searchParams = useSearchParams();

	const sorting = useMemo(() => {
		if (!externalSorting) {
			return undefined;
		}

		if (!jobsTableSortFieldsAllowed.includes(externalSorting.field as any)) {
			return undefined;
		}

		return externalSorting;
	}, [externalSorting]);

	const sortToggleFn = (field: string) => {
		if (!jobsTableSortFieldsAllowed.includes(field as any)) {
			return undefined;
		}

		return (direction: "asc" | "desc" | undefined) => {
			// Convert the search params to a record.
			let query: Record<string, string | string[] | null> =
				convertSearchParams(searchParams);

			if (direction === undefined) {
				query = {
					...query,
					sortField: null,
					sortDirection: null,
				};
			} else {
				query = {
					...query,
					sortField: field,
					sortDirection: direction,
				};
			}

			router.push(
				{
					query: Object.fromEntries(
						Object.entries(query).filter(([, value]) => value !== null),
					),
					pathname,
				},
				{ scroll: false },
			);
		};
	};

	const sortDirection = (field: string) => {
		if (!jobsTableSortFieldsAllowed.includes(field as any)) {
			return undefined;
		}

		if (sorting?.field !== field) {
			return undefined;
		}

		return sorting.direction;
	};

	return (
		<div className="w-full overflow-x-auto">
			<table className="">
				<JobsTableHead>
					<JobsTableHeadColumn
						direction={sortDirection("title")}
						onChangeDirection={sortToggleFn("title")}
					>
						{labels.title}
					</JobsTableHeadColumn>
					<JobsTableHeadColumn
						direction={sortDirection("entryLevel")}
						onChangeDirection={sortToggleFn("entryLevel")}
					>
						{labels.entryLevel}
					</JobsTableHeadColumn>
					<JobsTableHeadColumn
						direction={sortDirection("employmentType")}
						onChangeDirection={sortToggleFn("employmentType")}
					>
						{labels.employmentType}
					</JobsTableHeadColumn>
				</JobsTableHead>
				<tbody>
					{data.jobs.map((element) => (
						<JobsTableRow key={element.id}>
							<td>
								<Link href={element.link}>{element.title}</Link>
							</td>
							<td>Berufserfahrene</td>
							<td>Vollzeit</td>
						</JobsTableRow>
					))}
				</tbody>
			</table>
		</div>
	);
};
