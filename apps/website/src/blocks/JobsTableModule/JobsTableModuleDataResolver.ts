import payloadConfig from "@payload-config";
import { getPayload } from "payload";

import {
	jobsTableSortDirectionAllowed,
	jobsTableSortFieldsAllowed,
} from "@/blocks/JobsTableModule/utils";
import { defineBlockDataResolver } from "@/blocks/utils/block-data-resolver";
import { buildPagination } from "@/utils/pagination";
import { getPayloadCollectionMapping } from "@/utils/payload-collections-mapping";

import type { JobsTableModuleBlock } from "@/gen/payload/types";
import type { Where, Sort } from "payload";

const createSorting = (field: any, direction: any): Sort => {
	if (!field || !direction) {
		return [];
	}

	if (!jobsTableSortFieldsAllowed.includes(field)) {
		return [];
	}

	if (!jobsTableSortDirectionAllowed.includes(direction)) {
		return [];
	}

	return [`${direction === "asc" ? "" : "-"}${field}`];
};

export const JobsTableModuleDataResolver = defineBlockDataResolver(
	"jobs-table-module",
	async (ctx, block: JobsTableModuleBlock) => {
		const payload = await getPayload({ config: payloadConfig });
		const collectionMapping = getPayloadCollectionMapping(ctx, "jobs");

		const perPage = 16;
		const currentPage = Math.max(ctx.searchParams.p ?? 1, 1);

		const sortField = ctx.searchParams.sortField ?? undefined;
		const sortDirection = ctx.searchParams.sortDirection ?? undefined;

		const whereCondition: Where = {};
		const sorting = createSorting(sortField, sortDirection);

		const totalJobs = (
			await payload.count({
				collection: "jobs",
				// If we are not in preview mode, we only include published pages.
				where: whereCondition,
			})
		).totalDocs;

		const jobs = await payload.find({
			collection: "jobs",
			limit: perPage,
			pagination: true,
			page: currentPage,

			// If this is intended for preview mode, we include draft versions.
			draft: ctx.isPreview,

			// If we are not in preview mode, we only include published pages.
			where: whereCondition,
			sort: sorting,
		});

		return {
			jobs: jobs.docs.map((doc) => ({
				...doc,
				link:
					collectionMapping?.resolvers[ctx.locale].build({
						id: doc.id,
						slug: doc.slug!!,
					}) ?? "/",
			})),
			pagination: buildPagination({
				currentPage,
				totalPages: Math.ceil(totalJobs / perPage),
			}),
		};
	},
);
