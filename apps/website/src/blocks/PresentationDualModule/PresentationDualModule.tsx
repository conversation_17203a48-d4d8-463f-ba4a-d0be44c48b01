import { defineBlockComponent } from "@/blocks/utils";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";

import type { PresentationDualModuleBlock } from "@/gen/payload/types";

export const PresentationDualModule =
	defineBlockComponent<PresentationDualModuleBlock>(
		"presentation-dual-module",
		({ block }) => {
			return (
				<LModuleWrapper block={block}>
					<div className="flex flex-col lg:flex-row gap-16">
						{block.items?.map((item, idx) => (
							<div key={idx}>
								<h2 className="text-body font-display uppercase font-bold mb-8">
									<FLexicalRenderer
										content={item.title}
										firstParagraphOnly={true}
									/>
								</h2>

								{item.body && (
									<div className="text-body">
										<FLexicalRenderer content={item.body} />
									</div>
								)}
							</div>
						))}
					</div>
				</LModuleWrapper>
			);
		},
	);
