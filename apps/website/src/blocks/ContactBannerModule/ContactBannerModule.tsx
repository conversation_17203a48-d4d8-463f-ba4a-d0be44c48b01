import clsx from "clsx";

import { defineBlockComponent } from "@/blocks/utils";
import { FImage } from "@/components/FImage";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { Link } from "@/navigation";
import { normalizePhoneNumber } from "@/utils/phone-number";

import type { ContactBannerModuleBlock, Person } from "@/gen/payload/types";

interface PersonDetailsProps {
	person: Person;
}

export const ContactBannerModule =
	defineBlockComponent<ContactBannerModuleBlock>(
		"contact-banner-module",
		({ block }) => {
			// Define the other persons.
			const persons = block.person.filter(
				(it) => !!it && typeof it === "object",
			);

			return (
				<LModuleWrapper block={block} className="flex flex-col gap-y-20">
					{persons.map((person, idx) => {
						const isFirstPerson = idx === 0;

						return (
							<div
								key={idx}
								className="relative flex flex-col lg:flex-row gap-y-8 gap-x-16"
							>
								<div
									className={clsx(
										"flex-1 flex flex-col gap-y-16",
										isFirstPerson && "justify-between",
										!isFirstPerson && "justify-end",
									)}
								>
									{isFirstPerson && (
										<h1 className="font-display uppercase text-title-section hyphens-auto text-balance">
											<FLexicalRenderer
												content={block.title}
												firstParagraphOnly={true}
											/>
										</h1>
									)}

									<div className="flex flex-col gap-y-6">
										{isFirstPerson && (
											<div className="text-body text-balance">
												{block.body && (
													<FLexicalRenderer content={block.body} />
												)}
											</div>
										)}

										<div className="text-body">
											<div className="flex flex-col mb-2">
												<span className="font-bold">{person.title}</span>{" "}
												<span>{person.jobTitle}</span>
											</div>
											<div className="flex flex-col">
												{person.phoneNumber && (
													<span className="font-bold">
														<Link
															href={`tel:${normalizePhoneNumber(person.phoneNumber)}`}
														>
															{person.phoneNumber}
														</Link>
													</span>
												)}
												{person.emailAddress && (
													<span className="font-bold">
														<Link href={`mailto:${person.emailAddress}`}>
															{person.emailAddress}
														</Link>
													</span>
												)}
											</div>
										</div>
									</div>
								</div>

								<div className={clsx("flex-1 flex justify-center items-start")}>
									<div className="basis-5/7 aspect-square overflow-hidden bg-neutral-200">
										<FImage
											media={person.image as any}
											className="h-full w-full"
											quality={80}
											sizes="(min-width: 1440px) 720px, 50vw"
										/>
									</div>
								</div>
							</div>
						);
					})}
				</LModuleWrapper>
			);
		},
	);
