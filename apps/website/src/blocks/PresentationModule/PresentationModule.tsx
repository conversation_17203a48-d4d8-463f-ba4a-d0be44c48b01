import clsx from "clsx";

import { defineBlockComponent } from "@/blocks/utils";
import { FBlockRenderer } from "@/components/FBlockRenderer";
import { FImage } from "@/components/FImage";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";

import type { PresentationModuleBlock } from "@/gen/payload/types";

export const PresentationModule = defineBlockComponent<PresentationModuleBlock>(
	"presentation-module",
	({ block, ctx }) => {
		// Defines if the image should be in the flow or not.
		const imageInFlow = ["start", "end"].includes(block["image-placement"]);

		return (
			<LModuleWrapper block={block}>
				<div className={clsx("relative", [!imageInFlow && "px-8 py-16"])}>
					{!imageInFlow && (
						<div className="absolute inset-0 flex justify-center items-center bg-neutral-200 overflow-hidden">
							<div className="w-full h-full overflow-hidden bg-neutral-200">
								<FImage
									media={block.image as any}
									className="h-full w-full"
									quality={80}
									sizes="(min-width: 1440px) 1440px, 100vw"
									loading="lazy"
								/>
							</div>
						</div>
					)}

					<div
						className={clsx("relative flex flex-col lg:flex-row gap-16", {
							"lg:flex-row-reverse": block["image-placement"] === "start",
						})}
					>
						<div
							className={clsx(
								"basis-1/2 flex flex-col gap-y-12 justify-between",
							)}
						>
							<h1 className="font-display uppercase text-title-section hyphens-auto text-balance">
								<FLexicalRenderer
									content={block.title}
									firstParagraphOnly={true}
								/>
							</h1>

							<div className="flex flex-col gap-y-6">
								<div className="text-body text-balance">
									{block.body && <FLexicalRenderer content={block.body} />}
								</div>
								{block.actions && block.actions.length > 0 && (
									<div>
										{block.actions.map((action, idx) => (
											<FBlockRenderer block={action} key={idx} ctx={ctx} />
										))}
									</div>
								)}
							</div>
						</div>

						{imageInFlow && (
							<div className={clsx("basis-1/2")}>
								<div className="aspect-[16/12] w-full overflow-hidden bg-neutral-200">
									<FImage
										media={block.image as any}
										className="h-full w-full"
										quality={80}
										sizes="(min-width: 1440px) 720px, 50vw"
										loading="lazy"
									/>
								</div>
							</div>
						)}
					</div>
				</div>
			</LModuleWrapper>
		);
	},
);
