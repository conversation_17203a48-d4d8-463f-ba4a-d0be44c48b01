import { AnimatedPersonsGrid } from "@/blocks/PersonGridModule/components/AnimatedPersonsGrid";
import { StaticPersonsGrid } from "@/blocks/PersonGridModule/components/StaticPersonsGrid";
import { defineBlockComponent } from "@/blocks/utils";
import { LModuleWrapper } from "@/components/LModuleWrapper";

import type { PersonGridModuleDataResolver } from "@/blocks/PersonGridModule/PersonGridModuleDataResolver";
import type { PersonGridModuleBlock } from "@/gen/payload/types";
import type { ReactNode } from "react";

export const PersonGridModule = defineBlockComponent<
	PersonGridModuleBlock,
	typeof PersonGridModuleDataResolver
>("person-grid-module", ({ block, data }) => {
	let content: ReactNode;

	// Normalize the max rows. If 0, we want to show all rows.
	let maxRows = block.maxRows ?? undefined;
	if (maxRows === 0) {
		maxRows = undefined;
	}

	if (block.type === "animated") {
		content = (
			<AnimatedPersonsGrid
				persons={data.persons}
				intervalSeconds={block.animationUpdateInterval}
				maxRows={maxRows}
				placeholderRatio={0.25}
			/>
		);
	} else {
		// Static grid
		content = <StaticPersonsGrid persons={data.persons} maxRows={maxRows} />;
	}

	return <LModuleWrapper block={block}>{content}</LModuleWrapper>;
});
