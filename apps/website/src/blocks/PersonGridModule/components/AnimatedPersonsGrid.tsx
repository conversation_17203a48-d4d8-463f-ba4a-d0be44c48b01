"use client";
import { use<PERSON><PERSON>back, useEffect, useMemo, useReducer, useRef } from "react";

import { PersonGridCard } from "@/blocks/PersonGridModule/components/PersonGridCard";
import { PersonGridWrapper } from "@/blocks/PersonGridModule/components/PersonGridWrapper";
import { useItemsPerRow } from "@/blocks/PersonGridModule/utils";
import { useVisibilityChange } from "@/utils/hooks";

import type { Person } from "@/gen/payload/types";
import type { FC } from "react";

/**
 * Represents an item in the grid. This either displays a person or a placeholder.
 */
interface GridItem {
	/**
	 * Person to display or null if this is a placeholder.
	 */
	personId: string | null;

	/**
	 * Increment whenever the {@link #personId} changes.
	 */
	version: number;
}

/**
 * Resolved version of the grid item. This holds the actual person data.
 */
interface GridItemResolved extends GridItem {
	/**
	 * The resolved person data.
	 */
	resolvedPerson: Person | null;
}

interface AnimatedPersonsGridProps {
	persons: Person[];
	intervalSeconds?: number;
	maxRows?: number;
	placeholderRatio: number;
}

/**
 * Dispatch actions for the grid items.
 */
type GridItemsDispatch =
	| {
			type: "re-init";
	  }
	| {
			type: "set";
			items: { idx: number; personId: string | null }[];
	  };

interface AnimationAction {
	type: "replace-person" | "replace-placeholder" | "replace-person-with-person";
	index: number;
}

/**
 * Options for generating the next animation action.
 */
interface GetNextAnimationActionOpts {
	canAddPlaceholder: boolean;
	totalItems: number;
	previousAction: AnimationAction | null;
	isSupported: (action: AnimationAction) => boolean;
}

/**
 * Generates the next animation action based on the given options.
 *
 * @param opts The options for generating the next animation action.
 */
const getNextAnimationAction = (
	opts: GetNextAnimationActionOpts,
): AnimationAction => {
	// Define the available types.
	const availableTypes: AnimationAction["type"][] = [
		"replace-placeholder",
		"replace-person-with-person",
	];

	// Add the replace-person action if we can add a placeholder.
	if (opts.canAddPlaceholder) {
		availableTypes.push("replace-person");
	}

	// Keep generating actions until we find a supported one.
	while (true) {
		// Define the action.
		const action: AnimationAction = {
			type: availableTypes[Math.floor(Math.random() * availableTypes.length)],
			index: Math.floor(Math.random() * opts.totalItems),
		};

		if (
			opts.previousAction &&
			(opts.previousAction.type === action.type ||
				opts.previousAction.index === action.index)
		) {
			continue;
		}

		if (opts.isSupported(action)) {
			return action;
		}
	}
};

/**
 * Fisher - Yates shuffle in-place.
 * @param array The array to shuffle.
 * @returns The same array, shuffled.
 */
function shuffle<T>(array: T[]): T[] {
	for (let i = array.length - 1; i > 0; i--) {
		const j = Math.floor(Math.random() * (i + 1));
		[array[i], array[j]] = [array[j], array[i]];
	}
	return array;
}

/**
 * Creates an index of persons by their id.
 *
 * @param persons The persons to index.
 */
const createPersonsIndex = (persons: Person[]): Record<string, Person> => {
	const index: Record<string, Person> = {};

	for (const person of persons) {
		index[person.id] = person;
	}

	return index;
};

/**
 * Creates a grid item array with the given count.
 *
 * @param count The number of items to create.
 */
const createGridItemsByCount = (count: number): GridItem[] => {
	const items: GridItem[] = [];

	for (let i = 0; i < count; i++) {
		items.push({
			personId: null,
			version: 0,
		});
	}

	return items;
};

/**
 * Initializes the grid items with the given person ids.
 * The number of items given might be higher than the number of persons given.
 *
 * @param personIds The person ids to initialize the grid.
 * @param items The grid items to initialize.
 * @param placeholderRatio The ratio of placeholders in the grid.
 */
const initializeItems = (
	personIds: string[],
	items: GridItem[],
	placeholderRatio: number,
): void => {
	const total = items.length;

	// Calculate max placeholders based on ratio
	const maxPlaceholders = Math.floor(total * placeholderRatio);

	// Random placeholder count up to the cap
	const placeholderCount = Math.floor(Math.random() * (maxPlaceholders + 1));

	// Shuffle person IDs
	const shuffledIds = shuffle([...personIds]);

	// Determine how many persons to place
	const personCount = Math.min(shuffledIds.length, total - placeholderCount);

	// Create mixed array of person IDs and null placeholders
	const cells = [
		...shuffledIds.slice(0, personCount),
		...Array(total - personCount).fill(null),
	];

	// Final shuffle to distribute placeholders randomly
	shuffle(cells);

	// Assign results to items
	for (let i = 0; i < total; i++) {
		items[i] = {
			...items[i],
			personId: cells[i],
		};
	}
};

/**
 * Creates the initial grid items.
 *
 * @param personIds The person ids to initialize the grid.
 * @param itemsPerRow The number of items per row.
 * @param maxRows The maximum number of rows to display.
 * @param placeholderRatio The ratio of placeholders in the grid.
 */
const createInitialGridItems = (
	personIds: string[],
	itemsPerRow: number,
	maxRows: number | undefined,
	placeholderRatio: number,
	// eslint-disable-next-line max-params
): GridItem[] => {
	// The number of persons available to display.
	const personsCount = Math.max(
		personIds.length - Math.floor(Math.max(personIds.length * 0.1, 1)),
		1,
	);

	let items: GridItem[];

	// If the maxRows is not specified, we are going to show all persons.
	// We take the count of persons and round up to have full rows.
	if (!maxRows) {
		items = createGridItemsByCount(
			Math.ceil(personsCount / itemsPerRow) * itemsPerRow,
		);
	} else {
		// We are going to show a maximum of maxRows rows.
		items = createGridItemsByCount(
			Math.min(maxRows, Math.ceil(personsCount / itemsPerRow)) * itemsPerRow,
		);
	}

	// Initialize the items with the persons.
	initializeItems(personIds, items, placeholderRatio);

	return items;
};

export const AnimatedPersonsGrid: FC<AnimatedPersonsGridProps> = (props) => {
	// We want to pause the animation when the document is not visible.
	const documentVisibility = useVisibilityChange({
		ssrVisibilityState: "hidden",
	});

	const itemsPerRow = useItemsPerRow();
	const personsIndex = useMemo(
		() => createPersonsIndex(props.persons),
		[props.persons],
	);

	const lastAnimationRef = useRef<AnimationAction | null>(null);

	const [items, dispatchItems] = useReducer(
		(state: GridItem[], action: GridItemsDispatch) => {
			if (action.type === "re-init") {
				return createInitialGridItems(
					Object.keys(personsIndex),
					itemsPerRow,
					props.maxRows,
					props.placeholderRatio,
				);
			} else if (action.type === "set") {
				return state.map((it, idx) => {
					const item = action.items.find((it) => it.idx === idx);
					if (!item) {
						return it;
					}

					return {
						...it,
						personId: item.personId,
						version: it.version + 1,
					};
				});
			}

			return state;
		},
		[],
		() =>
			createInitialGridItems(
				Object.keys(personsIndex),
				itemsPerRow,
				props.maxRows,
				props.placeholderRatio,
			),
	);

	// Re-initialize the items when the itemsPerRow changes.
	// This will happen when the screen size changes mostly.
	useEffect(() => {
		dispatchItems({ type: "re-init" });
	}, [itemsPerRow]);

	// Function to run the animation.
	const runAnimation = useCallback(() => {
		// Find all persons that are not in the grid.
		const hiddenPersonIds = Object.keys(personsIndex).filter(
			(it) => !items.find((item) => item.personId === it),
		);

		// Define how many placeholders there can be at most.
		const maxPlaceholders = Math.floor(items.length * props.placeholderRatio);

		// Get the next animation action.
		const action = getNextAnimationAction({
			// We can add a placeholder if there are less than the max placeholders.
			canAddPlaceholder:
				items.filter((it) => !it.personId).length < maxPlaceholders,
			totalItems: items.length,
			previousAction: lastAnimationRef.current ?? null,
			isSupported: (action) => {
				if (action.type === "replace-person") {
					// We can only replace a person with a placeholder if there is a person.
					// Otherwise, it would replace a placeholder with a placeholder.
					return items[action.index].personId !== null;
				}

				return true;
			},
		});

		// Save the last animation action.
		lastAnimationRef.current = action;

		// Pick a random person not in the grid
		const randomOffGridPerson =
			hiddenPersonIds[Math.floor(Math.random() * hiddenPersonIds.length)];

		// - Interpret the action.

		dispatchItems({
			type: "set",
			items: [
				{
					idx: action.index,
					personId:
						action.type === "replace-person" ? null : randomOffGridPerson,
				},
			],
		});
	}, [items, personsIndex, props.placeholderRatio]);

	// Run the animation in an interval.
	useEffect(() => {
		// Only run the animation if the document is visible.
		if (documentVisibility !== "visible") {
			return;
		}

		const interval = setInterval(
			() => runAnimation(),
			(props.intervalSeconds ?? 2) * 1000,
		);

		return () => clearInterval(interval);
	}, [runAnimation, props.intervalSeconds, documentVisibility]);

	// Depend on the raw GridItems array to create resolved items based on it.
	// This way we can work on the raw items and not worry about resolving the persons.
	const resolvedItems = useMemo<GridItemResolved[]>(
		() =>
			items.map((it) => ({
				...it,
				// Use the index to resolve the person.
				resolvedPerson: it.personId ? personsIndex[it.personId] : null,
			})),
		[items, personsIndex],
	);

	return (
		<PersonGridWrapper>
			{resolvedItems.map((item, idx) => (
				<PersonGridCard
					key={`${idx}-${item.version}`}
					person={item.resolvedPerson}
				/>
			))}
		</PersonGridWrapper>
	);
};
