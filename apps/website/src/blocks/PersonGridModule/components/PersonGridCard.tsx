import clsx from "clsx";
import * as motion from "motion/react-client";

import { FImage } from "@/components/FImage";

import type { Person } from "@/gen/payload/types";
import type { FC } from "react";

interface PersonGridCardProps {
	/**
	 * The person or explicit null if this is a placeholder.
	 */
	person: Person | null;
}

/**
 * Displays a single person card for the grid. This might be used as a placeholder.
 */
export const PersonGridCard: FC<PersonGridCardProps> = ({ person }) => {
	return (
		<motion.div
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			exit={{ opacity: 0 }}
			transition={{ duration: 0.5 }}
			className={clsx(
				"aspect-square",
				"relative isolated group bg-highlighted",
			)}
		>
			{person && (
				<>
					<FImage
						media={person.image as any}
						className="h-full w-full absolute inset-0 object-cover"
						quality={85}
						sizes="(min-width: 640px) 50vw, (min-width: 1024px) 33vw, (min-width: 1440px) 480px, 100vw"
					/>

					<div
						className={clsx(
							"absolute inset-0 opacity-0 pointer-events-none p-4",
							"group-hover:opacity-100 group-hover:pointer-events-auto transition-opacity transition-config-default",
							"bg-black/60",
							"text-white",
						)}
					>
						<h3 className="font-display uppercase text-2xl mb-4">
							{person.title}
						</h3>

						{person.jobTitle && (
							<p className="text-body leading-snug">{person.jobTitle}</p>
						)}
					</div>
				</>
			)}
		</motion.div>
	);
};
