import clsx from "clsx";
import { AnimatePresence } from "motion/react";

import type { FC, ReactNode } from "react";

interface PersonGridWrapperProps {
	children?: ReactNode;
}

/**
 * Basic wrapper for the person grid that provides the grid layout and animation configs.
 */
export const PersonGridWrapper: FC<PersonGridWrapperProps> = ({ children }) => {
	return (
		<AnimatePresence>
			<div
				className={clsx(
					"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-16",
				)}
			>
				{children}
			</div>
		</AnimatePresence>
	);
};
