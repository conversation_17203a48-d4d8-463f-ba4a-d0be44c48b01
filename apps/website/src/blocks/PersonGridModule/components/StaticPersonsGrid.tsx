"use client";
import { useMemo } from "react";

import { PersonGridCard } from "@/blocks/PersonGridModule/components/PersonGridCard";
import { PersonGridWrapper } from "@/blocks/PersonGridModule/components/PersonGridWrapper";
import { useItemsPerRow } from "@/blocks/PersonGridModule/utils";

import type { Person } from "@/gen/payload/types";
import type { FC } from "react";

interface StaticPersonsGridProps {
	persons: Person[];
	/**
	 * Maximum number of rows to display.
	 * If not specified, there will be as many rows as needed to display the given array of persons.
	 */
	maxRows?: number;
}

export const StaticPersonsGrid: FC<StaticPersonsGridProps> = ({
	persons,
	maxRows,
}) => {
	// Items per row are dependent on the screen size.
	const itemsPerRow = useItemsPerRow();

	const displayPersons = useMemo(() => {
		if (!maxRows) return persons;

		return persons.slice(0, maxRows * itemsPerRow);
	}, [maxRows, itemsPerRow, persons]);

	// Calculate how many placeholders we need.
	// We know the number of total persons, and we need to always fill up a full row.
	const placeholderCount = useMemo(
		() =>
			Math.ceil(displayPersons.length / itemsPerRow) * itemsPerRow -
			displayPersons.length,
		[itemsPerRow, displayPersons.length],
	);

	return (
		<PersonGridWrapper>
			{displayPersons.map((it) => (
				<PersonGridCard key={it.id} person={it} />
			))}

			{Array.from({ length: placeholderCount }).map((_, idx) => (
				<PersonGridCard
					key={`placeholder-${itemsPerRow}-${idx}`}
					person={null}
				/>
			))}
		</PersonGridWrapper>
	);
};
