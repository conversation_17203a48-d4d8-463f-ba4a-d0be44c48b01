import payloadConfig from "@payload-config";
import { getPayload } from "payload";

import { defineBlockDataResolver } from "@/blocks/utils/block-data-resolver";

import type { PersonGridModuleBlock } from "@/gen/payload/types";
import type { Where } from "payload";

/**
 * Normalizes the given relation to always be an array of ids.
 *
 * @param input The input relation.
 */
const normalizeIdsOnly = (
	input: (string | { id: string })[] | undefined | null,
): string[] => {
	if (!input) {
		return [];
	}

	return input.map((it) => (typeof it === "string" ? it : it.id));
};

export const PersonGridModuleDataResolver = defineBlockDataResolver(
	"person-grid-module",
	async (ctx, block: PersonGridModuleBlock) => {
		const payload = await getPayload({ config: payloadConfig });

		const condition: Where = {};

		if (block.persons.type === "specific") {
			const includeIds = normalizeIdsOnly(block.persons.specific);

			// Only include the specific persons.
			condition.id = { in: includeIds };
		}

		const result = await payload.find({
			collection: "persons",

			// If this is intended for preview mode, we include draft versions.
			draft: ctx.isPreview,
			limit: 100,

			where: {
				and: [
					// If we are not in preview mode, we only include published pages.
					!ctx.isPreview ? { _status: { equals: "published" } } : {},

					// Exclude persons that are not supposed to be displayed.
					{ excludeFromDisplay: { equals: false } },

					condition,
				],
			},
			depth: 1,
		});

		let persons = result.docs;
		if (block.persons.type === "specific") {
			// Normalize the relations.
			const personIds = normalizeIdsOnly(block.persons.specific);

			// The documents must be in the same order as they are given by their ids.
			persons = persons.sort((a, b) => {
				return personIds.indexOf(a.id) - personIds.indexOf(b.id);
			});
		}

		return {
			persons: persons,
		};
	},
);
