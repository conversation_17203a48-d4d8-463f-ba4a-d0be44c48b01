import { defineBlockComponent } from "@/blocks/utils";
import { F<PERSON><PERSON><PERSON>enderer } from "@/components/FBlockRenderer";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";

import type { SectionHeaderModuleBlock } from "@/gen/payload/types";

export const SectionHeaderModule =
	defineBlockComponent<SectionHeaderModuleBlock>(
		"section-header-module",
		({ block, ctx }) => {
			return (
				<LModuleWrapper
					block={block}
					className="flex flex-col lg:flex-row lg:items-end gap-8"
				>
					<div className="flex-1">
						<h2 className="font-display uppercase text-title-section text-balance hyphens-auto">
							<FLexicalRenderer
								content={block.title}
								firstParagraphOnly={true}
							/>
						</h2>

						{block.body && (
							<div className="text-body mt-8">
								<FLexicalRenderer content={block.body} />
							</div>
						)}
					</div>
					{block.actions && block.actions.length > 0 && (
						<div className="lg:col-start-2">
							{block.actions.map((action, idx) => (
								<FBlockRenderer block={action} key={idx} ctx={ctx} />
							))}
						</div>
					)}
				</LModuleWrapper>
			);
		},
	);
