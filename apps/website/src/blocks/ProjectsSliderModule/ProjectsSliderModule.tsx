import clsx from "clsx";

import { defineBlockComponent } from "@/blocks/utils";
import { FImage } from "@/components/FImage";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { MCardSliderPersisted } from "@/components/MCardSlider";
import { Link } from "@/navigation";
import { buildPayloadLink } from "@/utils/payload-block-utils";
import { getPayloadCollectionMapping } from "@/utils/payload-collections-mapping";

import type { ProjectsSliderModuleBlock } from "@/gen/payload/types";

export const ProjectsSliderModule =
	defineBlockComponent<ProjectsSliderModuleBlock>(
		"projects-slider-module",
		async ({ block, ctx }) => {
			const collectionMapping = await getPayloadCollectionMapping(
				ctx,
				"projects",
			);

			return (
				<LModuleWrapper block={block}>
					<MCardSliderPersisted
						persistenceKey={`${block.blockType}-${block.id}`}
					>
						{block.projects.map((element) => {
							if (
								typeof element !== "object" ||
								typeof element.coverImage !== "object"
							) {
								return null;
							}

							// Build the link to the project.
							const link = buildPayloadLink({
								ctx,
								slug: collectionMapping?.resolvers[ctx.locale].build({
									id: element.id,
									slug: element.slug,
								}),
							});

							return (
								<div
									key={element.id}
									className={clsx("aspect-[16/12]", "relative group")}
								>
									<FImage
										media={element.coverImage}
										className="h-full w-full absolute inset-0 object-cover"
										quality={85}
										sizes="(min-width: 640px) 50vw, (min-width: 1024px) 33vw, (min-width: 1440px) 480px, 100vw"
									/>

									<div
										className={clsx(
											"absolute inset-0 opacity-0 pointer-events-none p-4",
											"group-hover:opacity-100 group-hover:pointer-events-auto transition-opacity transition-config-default",
											"bg-black/60",
											"text-white",
											"flex flex-col justify-between",
										)}
									>
										<div>
											<h3 className="font-display uppercase text-2xl mb-4">
												{element.title}
											</h3>
											<p className="text-body leading-snug">
												{element.addressCity}
											</p>
										</div>
										<div className="flex justify-end">
											<Link
												className="underline uppercase font-bold font-display"
												href={link}
											>
												MEHR
											</Link>
										</div>
									</div>
								</div>
							);
						})}
					</MCardSliderPersisted>
				</LModuleWrapper>
			);
		},
	);
