import payloadConfig from "@payload-config";
import { getPayload } from "payload";

import { defineBlockDataResolver } from "@/blocks/utils/block-data-resolver";
import { buildPagination } from "@/utils/pagination";

import type { ProjectsListModuleBlock } from "@/gen/payload/types";

export const ProjectsListModuleDataResolver = defineBlockDataResolver(
	"projects-list-module",
	async (ctx, block: ProjectsListModuleBlock) => {
		const payload = await getPayload({ config: payloadConfig });

		const perPage = block.pageSize;
		const currentPage = Math.max(ctx.searchParams.p ?? 1, 1);

		const totalProjectsCount = (
			await payload.count({
				collection: "projects",
				// If we are not in preview mode, we only include published pages.
				...(!ctx.isPreview && {
					where: { _status: { equals: "published" } },
				}),
			})
		).totalDocs;

		const projects = await payload.find({
			collection: "projects",
			limit: perPage,
			pagination: true,
			page: currentPage,

			// If this is intended for preview mode, we include draft versions.
			draft: ctx.isPreview,

			// If we are not in preview mode, we only include published pages.
			...(!ctx.isPreview && {
				where: { _status: { equals: "published" } },
			}),
		});

		return {
			projects: projects.docs,
			pagination: buildPagination({
				currentPage,
				totalPages: Math.ceil(totalProjectsCount / perPage),
			}),
		};
	},
);
