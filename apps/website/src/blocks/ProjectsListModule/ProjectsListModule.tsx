import clsx from "clsx";
import { Fragment } from "react";

import { defineBlockComponent } from "@/blocks/utils";
import { CPagination } from "@/components/CPagination";
import { CSectionSeparator } from "@/components/CSectionSeparator";
import { FImage } from "@/components/FImage";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { Link } from "@/navigation";
import { buildPayloadLink } from "@/utils/payload-block-utils/link";
import { getPayloadCollectionMapping } from "@/utils/payload-collections-mapping";

import type { ProjectsListModuleDataResolver } from "@/blocks/ProjectsListModule/ProjectsListModuleDataResolver";
import type { ProjectsListModuleBlock } from "@/gen/payload/types";

export const ProjectsListModule = defineBlockComponent<
	ProjectsListModuleBlock,
	typeof ProjectsListModuleDataResolver
>("projects-list-module", async ({ block, ctx, data }) => {
	const collectionMapping = getPayloadCollectionMapping(ctx, "projects");

	return (
		<LModuleWrapper block={block}>
			<div
				className={clsx(
					"grid gap-16 mb-16",
					"grid-cols-1 md:grid-cols-2 lg:grid-cols-[repeat(6,1fr)]",
				)}
			>
				{(() => {
					let groupSize = 3;
					let countInGroup = 0;
					let keyCounter = 0;

					return data.projects.map((element, idx) => {
						if (
							typeof element !== "object" ||
							typeof element.coverImage !== "object"
						) {
							return null;
						}

						const currentClass =
							groupSize === 3 ? "lg:col-span-2" : "lg:col-span-3";
						countInGroup++;
						const children = [];

						// Insert a row separator before a new group (except first)
						if (countInGroup === 1 && idx !== 0) {
							children.push(
								<div
									key={`separator-${keyCounter++}`}
									className="hidden lg:block col-span-full"
								>
									<CSectionSeparator />
								</div>,
							);
						}

						// Build the link to the project.
						const link = buildPayloadLink({
							ctx,
							slug: collectionMapping?.resolvers[ctx.locale].build({
								id: element.id,
								slug: element.slug,
							}),
						});

						children.push(
							<div
								key={element.id}
								className={clsx(
									"aspect-[16/12]",
									"relative group",
									currentClass,
								)}
							>
								<FImage
									media={element.coverImage}
									className="h-full w-full absolute inset-0 object-cover"
									quality={85}
									sizes="(min-width: 640px) 50vw, (min-width: 1024px) 33vw, (min-width: 1440px) 480px, 100vw"
								/>

								<div
									className={clsx(
										"absolute inset-0 opacity-0 pointer-events-none p-4",
										"group-hover:opacity-100 group-hover:pointer-events-auto transition-opacity transition-config-default",
										"bg-black/60",
										"text-white",
										"flex flex-col justify-between",
									)}
								>
									<div>
										<h3 className="font-display uppercase text-2xl mb-4">
											{element.title}
										</h3>
										<p className="text-body leading-snug">
											{element.addressCity}
										</p>
									</div>
									<div className="flex justify-end">
										<Link
											className="underline uppercase font-bold font-display"
											href={link}
										>
											MEHR
										</Link>
									</div>
								</div>
							</div>,
						);

						if (countInGroup === groupSize) {
							groupSize = groupSize === 3 ? 2 : 3;
							countInGroup = 0;
						}

						return (
							<Fragment key={`group-${keyCounter++}`}>{children}</Fragment>
						);
					});
				})()}
			</div>

			<CPagination
				currentPage={data.pagination.current}
				totalPages={data.pagination.total}
				nextPageLink={
					data.pagination.next
						? buildPayloadLink({
								ctx,
								searchParams: { p: data.pagination.next },
							})
						: undefined
				}
				prevPageLink={
					data.pagination.previous
						? buildPayloadLink({
								ctx,
								searchParams: { p: data.pagination.previous },
							})
						: undefined
				}
			/>
		</LModuleWrapper>
	);
});
