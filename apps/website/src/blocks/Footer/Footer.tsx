import { PayloadLexicalReactRenderer } from "@atelier-disko/payload-lexical-react-renderer";

import { defineBlockComponent } from "../utils";
import { CSocialIcon } from "@/components/CSocialIcon";
import { FPayloadLink } from "@/components/FPayloadLink";
import {
	<PERSON><PERSON><PERSON><PERSON>,
	MFooterBottomLine,
	MFooterBottomLineItem,
	MFooterGroup,
	MFooterItem,
} from "@/components/MFooter";

import type { FooterBlock } from "@/gen/payload/types";

export const Footer = defineBlockComponent<FooterBlock>(
	"footer",
	({ block, ctx }) => {
		return (
			<MFooter
				details={<PayloadLexicalReactRenderer content={block.details as any} />}
				bottomLine={
					block.bottomLine && (
						<MFooterBottomLine title={block.bottomLine.title}>
							{block.bottomLine.socialNetworks.map((it) => (
								<MFooterBottomLineItem key={it.id}>
									<FPayloadLink ctx={ctx} link={it.link}>
										<CSocialIcon type={it.type} className="h-8 w-8" />
									</FPayloadLink>
								</MFooterBottomLineItem>
							))}
						</MFooterBottomLine>
					)
				}
			>
				{block.groups.map((group) => (
					<MFooterGroup key={group.title} title={group.title ?? undefined}>
						{group.items.map((item) => (
							<MFooterItem key={item.label}>
								<FPayloadLink ctx={ctx} link={item.link}>
									{item.label}
								</FPayloadLink>
							</MFooterItem>
						))}
					</MFooterGroup>
				))}
			</MFooter>
		);
	},
);
