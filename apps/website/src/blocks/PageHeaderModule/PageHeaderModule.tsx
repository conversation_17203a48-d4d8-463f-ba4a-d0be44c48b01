import { defineBlockComponent } from "../utils";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";

import type { PageHeaderModuleBlock } from "@/gen/payload/types";

export const PageHeaderModule = defineBlockComponent<PageHeaderModuleBlock>(
	"page-header-module",
	({ block }) => {
		return (
			<LModuleWrapper block={block}>
				<div className="flex flex-col gap-y-12">
					<h1 className="font-display uppercase text-title-section text-balance">
						<FLexicalRenderer content={block.title} firstParagraphOnly={true} />
					</h1>
				</div>
			</LModuleWrapper>
		);
	},
);
