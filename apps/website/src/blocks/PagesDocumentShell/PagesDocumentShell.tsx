import { defineBlockComponent } from "@/blocks/utils";
import { LPageShellLayout } from "@/components/LPageShellLayout";

import type { DocumentShellBlock } from "@/blocks/utils/types";
import type { Page } from "@/gen/payload/types";

export const PagesDocumentShell = defineBlockComponent<
	DocumentShellBlock<Page, "pages">
>("pages-document-shell", ({ block, ctx }) => (
	<LPageShellLayout data={block.layout} ctx={ctx} />
));
