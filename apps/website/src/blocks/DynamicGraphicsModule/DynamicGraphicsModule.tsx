import { defineBlockComponent } from "@/blocks/utils";
import { FImage } from "@/components/FImage";
import { LModuleWrapper } from "@/components/LModuleWrapper";

import type { DynamicGraphicsModuleBlock } from "@/gen/payload/types";

export const DynamicGraphicsModule =
	defineBlockComponent<DynamicGraphicsModuleBlock>(
		"dynamic-graphics-module",
		({ block }) => {
			return (
				<LModuleWrapper
					block={block}
					className="flex justify-between gap-16 flex-col md:flex-row"
				>
					{block.items?.map((item) => (
						<div key={item.id}>
							<FImage media={item.image as any} />
						</div>
					))}
				</LModuleWrapper>
			);
		},
	);
