import payloadConfig from "@payload-config";
import { getPayload } from "payload";

import { defineBlockDataResolver } from "@/blocks/utils/block-data-resolver";
import { adjustLegacyData } from "@/utils/legacy";

export const NewsDocumentShellDataResolver = defineBlockDataResolver(
	"news-document-shell",
	async (ctx) => {
		const payload = await getPayload({ config: payloadConfig });

		const data = await payload.findGlobal({
			slug: "news-shell",
			locale: ctx.locale,
		});

		return adjustLegacyData(data);
	},
);
