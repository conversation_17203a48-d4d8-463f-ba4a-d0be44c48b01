import { defineBlockComponent } from "@/blocks/utils";
import { LPageShellLayout } from "@/components/LPageShellLayout";

import type { NewsDocumentShellDataResolver } from "@/blocks/NewsDocumentShell/NewsDocumentShellDataResolver";
import type { DocumentShellBlock } from "@/blocks/utils/types";
import type { News } from "@/gen/payload/types";

export const NewsDocumentShell = defineBlockComponent<
	DocumentShellBlock<News, "news">,
	typeof NewsDocumentShellDataResolver
>("news-document-shell", ({ ctx, data }) => {
	return <LPageShellLayout data={data.layout} ctx={ctx} />;
});
