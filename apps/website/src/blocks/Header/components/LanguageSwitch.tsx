"use client";
import clsx from "clsx";
import { useLocale } from "next-intl";

import { locales } from "@/i18n";
import { Link } from "@/navigation";

import type { AvailableLocale } from "@/i18n";
import type { PayloadDocumentSlugs } from "@/utils/payload-link-lookup/types";
import type { FC } from "react";

interface Props {
	availableLanguages: readonly AvailableLocale[];
	localizedPageSlugs?: Partial<PayloadDocumentSlugs>;
}

export const LanguageSwitch: FC<Props> = (props) => {
	const currentLocale = useLocale();

	return (
		<div className="flex flex-row items-center -mx-2 font-display">
			{locales.map((it, idx) => {
				const isCurrent = it === currentLocale;
				let targetHref: string | undefined;

				if (props.localizedPageSlugs && props.localizedPageSlugs[it]) {
					targetHref = props.localizedPageSlugs[it];
				}

				return (
					<Link
						href={targetHref ?? "/"}
						locale={it}
						key={idx}
						className={clsx(
							"px-2 leading-none not-last:border-r border-neutral-500",
							"text-base",
							isCurrent && "font-bold",
						)}
					>
						{it.toUpperCase()}
					</Link>
				);
			})}
		</div>
	);
};
