import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";

import type { FC } from "react";

export const SearchInput: FC = () => {
	return (
		<form method="get" action="/search/">
			<div className="flex flex-row border border-neutral-400 h-6">
				<input
					name="q"
					type="text"
					className="text-sm focus:outline-none focus:ring-0 px-2 leading-0 align-middle"
					placeholder="Suche..."
				/>
				<button type="submit" className="px-2 cursor-pointer text-neutral-600">
					<MagnifyingGlassIcon className="h-4 w-4" />
				</button>
			</div>
		</form>
	);
};
