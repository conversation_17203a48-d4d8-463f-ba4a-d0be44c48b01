import clsx from "clsx";

import type { <PERSON> } from "react";

interface Props {
	open?: boolean;
	onToggle?: () => void;
}

export const Hamburger: FC<Props> = (props) => {
	return (
		<div className="p-2">
			<div
				className={clsx("ham", { "ham-active": props.open })}
				onClick={() => props.onToggle?.()}
			>
				<div className="ham-box">
					<div className="ham-inner" />
				</div>
			</div>
		</div>
	);
};
