"use client";
import { Dialog, DialogPanel } from "@headlessui/react";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import clsx from "clsx";
import { AnimatePresence } from "motion/react";
import * as motion from "motion/react-client";
import { Children, type ComponentProps, useState } from "react";
import AnimateHeight from "react-animate-height";

import { Hamburger } from "@/blocks/Header/components/Hamburger";
import { CSectionSeparator } from "@/components/CSectionSeparator";
import { LContainer } from "@/components/LContainer";
import { Link } from "@/navigation";

import type { FC, ReactNode, MouseEventHandler } from "react";

type BasicLinkProps = Pick<
	ComponentProps<typeof Link>,
	"href" | "target" | "onClick"
>;

interface Props {
	children: ReactNode;

	languageSwitch?: ReactNode;
	socials?: ReactNode;
}

interface GroupProps {
	title: ReactNode;
	children: ReactNode;
	isActive?: boolean;
}

interface GroupItemProps extends BasicLinkProps {
	children: ReactNode;
	isActive?: boolean;
}

interface ItemProps extends BasicLinkProps {
	children: ReactNode;
	isActive?: boolean;
}

export const MobileMenuGroup: FC<GroupProps> = (props) => {
	const [isOpen, setOpen] = useState(false);

	return (
		<div className="">
			<div
				className="flex items-center justify-between py-3 cursor-pointer"
				onClick={() => setOpen((v) => !v)}
			>
				<div
					className={clsx(
						"text-black font-display text-lg uppercase",
						props.isActive && "font-bold",
					)}
				>
					{props.title}
				</div>

				<div>
					<ChevronDownIcon
						className={clsx("h-6 w-6", "transition duration-200 ease-in-out", {
							"rotate-180": isOpen,
						})}
					/>
				</div>
			</div>

			<AnimateHeight
				height={isOpen ? "auto" : 0}
				easing="ease-in-out"
				duration={200}
			>
				<div className="pb-6 pl-2">{props.children}</div>
			</AnimateHeight>
		</div>
	);
};

export const MobileMenuGroupItem: FC<GroupItemProps> = ({
	children,
	isActive,
	...linkProps
}) => {
	return (
		<Link
			className={clsx(
				"block py-2 font-display text-black",
				isActive && "font-bold",
			)}
			{...linkProps}
		>
			{children}
		</Link>
	);
};

export const MobileMenuItem: FC<ItemProps> = (props) => {
	return (
		<Link
			className={clsx(
				"block text-black font-display text-lg uppercase py-3",
				props.isActive && "font-bold",
			)}
			{...props}
		>
			{props.children}
		</Link>
	);
};

export const MobileMenu: FC<Props> = (props) => {
	const [isOpen, setOpen] = useState(false);

	const containerOnClickEvent: MouseEventHandler<HTMLDivElement> = (event) => {
		if (!(event.target instanceof HTMLAnchorElement)) {
			return;
		}

		// Close the dialog box when a link is clicked.
		setOpen(false);
	};

	return (
		<>
			<div className="-mx-2 -mb-1.5">
				<Hamburger open={isOpen} onToggle={() => setOpen((v) => !v)} />
			</div>

			<AnimatePresence>
				{isOpen && (
					<Dialog
						open={true}
						onClose={() => setOpen(false)}
						className="relative z-30"
					>
						<motion.div
							initial="hidden"
							animate="visible"
							exit={{ opacity: 0 }}
							variants={{
								hidden: { opacity: 0 },
								visible: {
									opacity: 1,
									transition: {
										duration: 0.25,
										when: "beforeChildren",
										staggerChildren: 0.15,
									},
								},
							}}
							className="fixed inset-0 pt-18 flex bg-white"
						>
							<DialogPanel className="w-full h-full flex flex-col">
								<LContainer
									className="flex flex-col w-full h-full overflow-y-auto"
									onClick={containerOnClickEvent}
								>
									{Children.map(props.children, (children, idx) => (
										<motion.div
											variants={{
												hidden: {
													opacity: 0,
													translateY: 10,
													filter: "blur10px)",
												},
												visible: {
													opacity: 1,
													translateY: 0,
													filter: "blur(0px)",
													transition: { duration: 0.25, ease: "circOut" },
												},
											}}
											key={idx}
										>
											{children}
										</motion.div>
									))}
								</LContainer>

								<LContainer className="w-full">
									<CSectionSeparator />

									<div className="py-4 flex flex-row justify-between">
										{props.socials}
										{props.languageSwitch}
									</div>
								</LContainer>
							</DialogPanel>
						</motion.div>
					</Dialog>
				)}
			</AnimatePresence>
		</>
	);
};
