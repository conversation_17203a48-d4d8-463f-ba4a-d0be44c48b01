import { getPayloadDocumentId } from "@/utils/payload-block-utils";
import { getPayloadCollectionMapping } from "@/utils/payload-collections-mapping";

import type { HeaderBlockActiveState, LinkField } from "@/gen/payload/types";
import type { PayloadBlockContext } from "@/utils/payload-block";
import type { CollectionSlug } from "payload";

interface ItemPartial {
	link?: LinkField;
	children?: ItemPartial[] | null;
	activeState?: HeaderBlockActiveState;
}

/**
 * Checks if the given item is active based on the link.
 *
 * @param ctx
 * @param input
 */
const isItemActiveWithLink = (
	ctx: PayloadBlockContext,
	input: ItemPartial,
): boolean => {
	if (!input.link) {
		return false;
	}

	if (input.link.type !== "reference") {
		return false;
	}

	const currentDocumentId = getPayloadDocumentId(ctx);
	const referenceValue = input.link.reference?.value;

	if (typeof referenceValue === "string") {
		// Relation is not loaded, the value is the id.
		return referenceValue === currentDocumentId;
	} else if (referenceValue?.id) {
		// Relation is loaded, the value is the document, and we can use the id.
		return referenceValue.id === currentDocumentId;
	}

	// Default to false.
	return false;
};

/**
 * Checks if the given item is active based on the active state.
 *
 * @param ctx
 * @param input
 */
const isItemActiveWithActiveState = (
	ctx: PayloadBlockContext,
	input: ItemPartial,
): boolean => {
	if (!input.activeState) {
		return false;
	}

	// If no collection is defined, we can't check.
	if (!input.activeState.collection) {
		return false;
	}

	// Fetch the collection mapping of the given collection.
	const mapping = getPayloadCollectionMapping(
		ctx,
		input.activeState.collection as CollectionSlug,
	);
	if (!mapping) {
		return false;
	}

	// Check if the current path matches the collection mapping.
	return !!mapping.resolvers[ctx.locale].match(ctx.path);
};

/**
 * Checks if the given item is active. This might be a leaf or a group.
 * A group is active if one of its children is active.
 *
 * @param ctx The context to check against.
 * @param input The item to check.
 */
export const isItemActive = (
	ctx: PayloadBlockContext,
	input: ItemPartial,
): boolean => {
	const isLeaf = !input.children?.length;

	if (!isLeaf) {
		return input.children?.some((it) => isItemActive(ctx, it)) ?? false;
	}

	// If it's a leaf, check if it's active based on the link or the active state.
	return (
		isItemActiveWithLink(ctx, input) || isItemActiveWithActiveState(ctx, input)
	);
};
