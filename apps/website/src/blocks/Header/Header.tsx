import { <PERSON>over, <PERSON>over<PERSON>utton, PopoverPanel } from "@headlessui/react";
import clsx from "clsx";

import { LanguageSwitch } from "./components/LanguageSwitch";
import {
	MobileMenu,
	MobileMenuGroup,
	MobileMenuGroupItem,
	MobileMenuItem,
} from "@/blocks/Header/components/MobileMenu";
import { SearchInput } from "@/blocks/Header/components/SearchInput";
import { isItemActive } from "@/blocks/Header/utils";
import { defineBlockComponent } from "@/blocks/utils";
import { CLogo } from "@/components/CLogo";
import { CSectionSeparator } from "@/components/CSectionSeparator";
import { CSocialIcon } from "@/components/CSocialIcon";
import { LContainer } from "@/components/LContainer";
import { locales } from "@/i18n";
import { Link } from "@/navigation";
import {
	getPayloadDocument,
	resolvePayloadLink,
} from "@/utils/payload-block-utils";
import { lookupPayloadDocumentSlugs } from "@/utils/payload-link-lookup";

import type { HeaderBlock } from "@/gen/payload/types";
import type { PayloadDocumentSlugs } from "@/utils/payload-link-lookup/types";
import type { ComponentProps, FC, ReactNode } from "react";

type BasicLinkProps = Pick<
	ComponentProps<typeof Link>,
	"href" | "target" | "onClick"
>;

type ItemProps = BasicLinkProps & {
	children: ReactNode;
	isActive?: boolean;
};

interface GroupProps {
	title?: string;
	children?: ReactNode;
	isActive?: boolean;
}

type GroupItemProps = BasicLinkProps & {
	children?: ReactNode;
	isActive?: boolean;
};

const hoverHighlightClassNames =
	"duration-150 ease-in-out hover:text-highlighted";

const HeaderItem: FC<ItemProps> = (props) => {
	return (
		<Link
			href={props.href}
			className={clsx(
				"text-base leading-none uppercase px-4 py-2",
				hoverHighlightClassNames,
				props.isActive && "font-bold",
			)}
		>
			{props.children}
		</Link>
	);
};

const HeaderGroup: FC<GroupProps> = (props) => {
	return (
		<Popover className="relative">
			<PopoverButton
				className={clsx(
					"flex items-center text-base uppercase cursor-pointer outline-none leading-none text-nowrap px-4 py-2",
					hoverHighlightClassNames,
					props.isActive && "font-bold",
				)}
			>
				{props.title}
			</PopoverButton>
			{props.children && (
				<PopoverPanel
					transition
					className={clsx(
						"absolute z-10 left-0 top-full mt-6 origin-top-right bg-white/95 backdrop-blur-sm px-4 py-3 min-w-44",
						"transition data-[closed]:translate-y-1 data-[closed]:opacity-0 data-[enter]:duration-200 data-[leave]:duration-150 data-[enter]:ease-out data-[leave]:ease-in",
					)}
				>
					{props.children}
					<CSectionSeparator className="mt-2" />
				</PopoverPanel>
			)}
		</Popover>
	);
};

const HeaderGroupItem: FC<GroupItemProps> = ({
	children,
	isActive,
	...linkProps
}) => {
	return (
		<Link
			{...linkProps}
			className={clsx(
				"block py-2 text-sm text-black",
				"text-nowrap uppercase",
				hoverHighlightClassNames,
				isActive && "font-bold",
			)}
		>
			{children}
		</Link>
	);
};

export const Header = defineBlockComponent<HeaderBlock>(
	"header",
	({ block, ctx }) => {
		let localizedPageSlugs: Partial<PayloadDocumentSlugs> = {};

		const document = getPayloadDocument(ctx);
		if (document) {
			localizedPageSlugs =
				lookupPayloadDocumentSlugs({ ctx, pageId: document.id }) ?? {};
		}

		return (
			<header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm">
				<LContainer className="h-18 lg:h-28 flex flex-col">
					<div className="flex-1 font-display">
						<div className="flex items-end pb-4 lg:pb-6 h-full">
							<Link href="/" className="flex items-center">
								<CLogo className="h-6 lg:h-10" />
							</Link>

							<div
								className={clsx(
									"h-12 flex-col flex-1 items-end justify-end gap-y-5",
									"hidden lg:flex",
								)}
							>
								<div className="flex flex-row gap-x-8">
									{(block.configuration.enableSearch ?? true) && (
										<SearchInput />
									)}

									{block.socials?.map((social) => (
										<Link
											key={social.id}
											{...resolvePayloadLink({
												ctx,
												link: social.link,
											})}
										>
											<CSocialIcon type={social.type} className="h-6" />
										</Link>
									))}

									<LanguageSwitch
										availableLanguages={locales}
										localizedPageSlugs={localizedPageSlugs}
									/>
								</div>

								<div
									className={clsx(
										"flex flex-row items-center justify-center text-neutral-950 -mx-4 -my-2",
									)}
								>
									{block.items.map((item) => {
										if (item.children?.length) {
											return (
												<HeaderGroup
													key={item.label}
													title={item.label}
													isActive={isItemActive(ctx, item)}
												>
													{item.children.map((it) => (
														<HeaderGroupItem
															key={it.label}
															isActive={isItemActive(ctx, it)}
															{...resolvePayloadLink({ ctx, link: it.link })}
														>
															{it.label}
														</HeaderGroupItem>
													))}
												</HeaderGroup>
											);
										} else {
											return (
												<HeaderItem
													key={item.label}
													isActive={isItemActive(ctx, item)}
													{...resolvePayloadLink({
														ctx,
														link: item.link,
													})}
												>
													{item.label}
												</HeaderItem>
											);
										}
									})}
								</div>
							</div>

							<div className={clsx("flex lg:hidden justify-end w-full")}>
								<MobileMenu
									languageSwitch={
										<LanguageSwitch
											availableLanguages={locales}
											localizedPageSlugs={localizedPageSlugs}
										/>
									}
									socials={block.socials?.map((social) => (
										<Link
											key={social.id}
											{...resolvePayloadLink({
												ctx,
												link: social.link,
											})}
										>
											<CSocialIcon type={social.type} className="h-6" />
										</Link>
									))}
								>
									{block.items.map((item) => {
										if (item.children?.length) {
											return (
												<MobileMenuGroup
													key={item.id}
													title={item.label}
													isActive={isItemActive(ctx, item)}
												>
													{item.children.map((it) => (
														<MobileMenuGroupItem
															key={it.id}
															isActive={isItemActive(ctx, it)}
															{...resolvePayloadLink({ ctx, link: it.link })}
														>
															{it.label}
														</MobileMenuGroupItem>
													))}
												</MobileMenuGroup>
											);
										} else {
											return (
												<MobileMenuItem
													key={item.id}
													isActive={isItemActive(ctx, item)}
													{...resolvePayloadLink({
														ctx,
														link: item.link,
													})}
												>
													{item.label}
												</MobileMenuItem>
											);
										}
									})}
								</MobileMenu>
							</div>
						</div>
					</div>
					{/* Height 2 units */}
					<CSectionSeparator />
				</LContainer>
			</header>
		);
	},
);
