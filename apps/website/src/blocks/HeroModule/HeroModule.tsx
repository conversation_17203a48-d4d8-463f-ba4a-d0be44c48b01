import clsx from "clsx";
import * as motion from "motion/react-client";

import { defineBlockComponent } from "@/blocks/utils";
import { FBlockRenderer } from "@/components/FBlockRenderer";
import { FImage } from "@/components/FImage";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LContainer } from "@/components/LContainer";

import type { HeroModuleBlock } from "@/gen/payload/types";

export const HeroModule = defineBlockComponent<HeroModuleBlock>(
	"hero-module",
	({ block, ctx }) => {
		return (
			<LContainer>
				<div className="flex flex-row gap-x-16">
					<div className="base-1/2 flex-1 flex flex-col gap-y-12 justify-between">
						<motion.h1
							initial={{ opacity: 0, filter: "blur(3px)", translateY: "-10px" }}
							animate={{ opacity: 1, filter: "blur(0px)", translateY: "0px" }}
							transition={{ duration: 0.75, delay: 0.25 }}
							className="font-display uppercase text-title-hero hyphens-auto text-balance"
						>
							<FLexicalRenderer
								content={block.title}
								firstParagraphOnly={true}
							/>
						</motion.h1>

						<motion.div
							initial={{ opacity: 0, filter: "blur(3px)" }}
							animate={{ opacity: 1, filter: "blur(0px)" }}
							transition={{ duration: 0.25, delay: 1 }}
							className="flex flex-col gap-y-6"
						>
							<div className="text-body hyphens-auto text-balance">
								{block.body && <FLexicalRenderer content={block.body} />}
							</div>
							{block.actions && block.actions.length > 0 && (
								<div>
									{block.actions.map((action, idx) => (
										<FBlockRenderer block={action} key={idx} ctx={ctx} />
									))}
								</div>
							)}
						</motion.div>
					</div>

					<div className="base-1/2 flex-1 hidden lg:block">
						<motion.div
							className={clsx(
								"aspect-[16/14] w-full flex justify-center items-center overflow-hidden",
								block.imageSize === "contained-default"
									? "bg-highlighted"
									: "bg-neutral-200",
							)}
							initial={{ opacity: 0, filter: "blur(3px)" }}
							animate={{ opacity: 1, filter: "blur(0px)" }}
							transition={{ duration: 0.25, delay: 1.5, ease: "easeOut" }}
						>
							<FImage
								media={block.image as any}
								className={clsx(
									block.imageSize === "contained-default"
										? "max-h-96 h-full w-auto"
										: "w-full h-full",
								)}
								quality={80}
								sizes="(min-width: 1440px) 720px, 50vw"
								priority={true}
								loading="eager"
							/>
						</motion.div>
					</div>
				</div>
			</LContainer>
		);
	},
);
