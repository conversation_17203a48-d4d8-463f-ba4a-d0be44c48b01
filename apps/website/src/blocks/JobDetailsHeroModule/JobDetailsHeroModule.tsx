import { defineBlockComponent } from "@/blocks/utils";
import { CFactsList, CFactsListItem } from "@/components/CFactsList";
import { CSectionSeparator } from "@/components/CSectionSeparator";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { getPayloadDocument } from "@/utils/payload-block-utils";

import type { JobDetailsHeroModuleBlock } from "@/gen/payload/types";

/**
 * Creates a list item for the given label and value.
 * If the value is not defined, null is returned.
 *
 * @param label The label.
 * @param value The value.
 */
const createItemFor = (label: string, value: string | undefined | null) => {
	if (!value) {
		return null;
	}

	return (
		<CFactsListItem key={label} title={label}>
			{value}
		</CFactsListItem>
	);
};

export const JobDetailsHeroModule =
	defineBlockComponent<JobDetailsHeroModuleBlock>(
		"job-details-hero-module",
		({ block, ctx }) => {
			const job = getPayloadDocument(ctx, "jobs");

			return (
				<LModuleWrapper
					block={block}
					className="flex flex-col lg:items-end gap-8"
				>
					<div className="flex flex-col">
						<h1 className="font-display text-title-section uppercase">
							{job.title}
						</h1>

						<div className="w-52 mt-12">
							<CSectionSeparator />
						</div>
					</div>

					<div className="w-full grid lg:grid-cols-2 gap-x-16 gap-y-12">
						<CFactsList className="lg:col-start-2">
							{createItemFor(block.labelLocation, job.location)}
							{createItemFor(block.labelEmploymentType, job.employmentType)}
							{createItemFor(block.labelTimeLimitation, job.timeLimitation)}
							{createItemFor(block.labelStartDate, job.startDate)}
							{createItemFor(block.labelDepartment, job.department)}
							{createItemFor(block.labelEntryLevel, job.entryLevel)}
						</CFactsList>
					</div>
				</LModuleWrapper>
			);
		},
	);
