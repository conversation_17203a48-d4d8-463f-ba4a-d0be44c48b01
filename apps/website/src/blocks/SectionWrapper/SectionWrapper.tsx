import clsx from "clsx";

import { SectionAnimated } from "@/blocks/SectionWrapper/components/SectionAnimated";
import { defineBlockComponent } from "@/blocks/utils";
import { FBlockRenderer } from "@/components/FBlockRenderer";
import { isFirstSectionExtension } from "@/utils/payload-block";

import type { SectionWrapperBlock } from "@/gen/payload/types";

export const SectionWrapper = defineBlockComponent<SectionWrapperBlock>(
	"section-wrapper",
	({ block, ctx }) => {
		const isFirstSection = isFirstSectionExtension.get(ctx);
		const showDivider = block.layout?.showDivider ?? true;

		// Check all the modules if they can be rendered.
		const children = block.modules
			// Check if the block can be rendered using the provided function.
			.filter((block) => FBlockRenderer.canRender({ block, ctx }))
			.map((module) => (
				<FBlockRenderer block={module} key={module.id} ctx={ctx} />
			));

		// If none of the children can be rendered, we don't render the section at all.
		if (children.length === 0) {
			return null;
		}

		return (
			<SectionAnimated
				disableAnimation={isFirstSection}
				showDivider={showDivider}
				sectionId={block.identifier ?? undefined}
			>
				<div
					className={clsx(
						"pt-14 pb-14 md:pt-20 md:pb-20 group-last-of-type/section:pb-0",
						"space-y-16 lg:space-y-20",
						!showDivider && "!pb-0",
					)}
				>
					{children}
				</div>
			</SectionAnimated>
		);
	},
);
