import * as motion from "motion/react-client";

import { CSectionSeparator } from "@/components/CSectionSeparator";
import { LContainer } from "@/components/LContainer";

import type { FC, ReactNode } from "react";

interface Props {
	children: ReactNode;
	sectionId?: string;
	disableAnimation?: boolean;
	showDivider?: boolean;
}

export const SectionAnimated: FC<Props> = ({
	children,
	disableAnimation,
	showDivider,
	sectionId,
}) => {
	const baseSectionClassName = "bg-white group/section";

	let element = children;

	const divider = (showDivider ?? true) && (
		<LContainer className="group-last-of-type/section:hidden">
			<CSectionSeparator />
		</LContainer>
	);

	if (disableAnimation) {
		// If the animation is disabled, we just render the section without any animation.
		element = (
			<section className={baseSectionClassName} id={sectionId}>
				{element}
				{divider}
			</section>
		);
	} else {
		// Otherwise, we wrap the section with the motion section.
		element = (
			<section className={baseSectionClassName} id={sectionId}>
				<motion.div
					viewport={{ once: true, margin: "-25px" }}
					initial={{ opacity: 0, filter: "blur(1px)" }}
					whileInView={{ opacity: 1, filter: "blur(0px)" }}
					transition={{ duration: 0.4, ease: [0.65, 0, 0.35, 1] }}
				>
					{element}
				</motion.div>
				{divider}
			</section>
		);
	}

	return element;
};
