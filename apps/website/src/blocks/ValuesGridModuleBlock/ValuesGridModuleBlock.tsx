import clsx from "clsx";

import { defineBlockComponent } from "@/blocks/utils";
import { CSquareCardValue } from "@/components/CSquareCardValue";
import { FImage } from "@/components/FImage";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { MSquareCardGrid } from "@/components/MSquareCardGrid";

import type { ValuesGridModuleBlock } from "@/gen/payload/types";

export const ValuesGridModule = defineBlockComponent<ValuesGridModuleBlock>(
	"values-grid-module",
	({ block, ctx }) => {
		return (
			<LModuleWrapper block={block} size="small">
				<MSquareCardGrid>
					{block.items.map((item, idx) => (
						<CSquareCardValue
							key={idx}
							image={
								<FImage media={item.image as any} className="w-full h-full" />
							}
							title={
								<FLexicalRenderer
									content={item.title}
									firstParagraphOnly={true}
								/>
							}
							body={<FLexicalRenderer content={item.body} />}
						/>
					))}

					<div
						className={clsx(
							"bg-highlighted p-10 h-full w-full flex justify-center items-center",
						)}
					>
						<FImage
							media={block.placeholderImage as any}
							className={clsx("max-h-96 h-full w-auto")}
							quality={80}
							sizes="(min-width: 1440px) 720px, 50vw"
							loading="lazy"
						/>
					</div>
				</MSquareCardGrid>
			</LModuleWrapper>
		);
	},
);
