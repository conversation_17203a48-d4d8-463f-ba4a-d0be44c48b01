import { defineBlockComponent } from "@/blocks/utils";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { getPayloadDocument } from "@/utils/payload-block-utils";

import type { NewsDetailsHeroModuleBlock } from "@/gen/payload/types";

export const NewsDetailsBodyModule =
	defineBlockComponent<NewsDetailsHeroModuleBlock>(
		"news-details-body-module",
		({ block, ctx }) => {
			const news = getPayloadDocument(ctx, "news");

			return (
				<LModuleWrapper
					block={block}
					className="md:columns-2 gap-16 prose prose-default max-w-full"
				>
					<FLexicalRenderer content={news.body} />
				</LModuleWrapper>
			);
		},
	);
