import clsx from "clsx";
import { getFormatter } from "next-intl/server";

import { defineBlockComponent } from "@/blocks/utils";
import { FImage } from "@/components/FImage";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { getPayloadDocument } from "@/utils/payload-block-utils";

import type { NewsDetailsHeroModuleBlock } from "@/gen/payload/types";

export const NewsDetailsHeroModule =
	defineBlockComponent<NewsDetailsHeroModuleBlock>(
		"news-details-hero-module",
		async ({ block, ctx }) => {
			const news = getPayloadDocument(ctx, "news");
			const format = await getFormatter();

			const formattedPublishDate = format.dateTime(new Date(news.publishDate), {
				year: "numeric",
				month: "2-digit",
				day: "2-digit",
			});

			const translatedCategory = block.categoryTranslations?.find(
				(it) => it.key === news.category,
			)?.value;

			return (
				<LModuleWrapper block={block}>
					<div className="flex flex-row gap-x-16">
						<div className="base-1/2 flex-1 flex flex-col gap-y-6 justify-end">
							<div className="flex flex-col gap-y-6">
								<div className="text-body hyphens-auto text-balance">
									{formattedPublishDate} {translatedCategory}
								</div>
							</div>

							<h1 className="font-display uppercase text-body-title hyphens-auto text-balance">
								{news.title}
							</h1>
						</div>

						<div className="base-1/2 flex-1 hidden lg:block">
							<div
								className={clsx(
									"aspect-[16/14] w-full flex justify-center items-center overflow-hidden bg-neutral-200",
								)}
							>
								<FImage
									media={news.coverImage as any}
									className={clsx("w-full h-full")}
									quality={80}
									sizes="(min-width: 1440px) 720px, 50vw"
									priority={true}
									loading="eager"
								/>
							</div>
						</div>
					</div>
				</LModuleWrapper>
			);
		},
	);
