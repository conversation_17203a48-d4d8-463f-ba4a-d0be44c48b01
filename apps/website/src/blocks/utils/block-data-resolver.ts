import type { BaseBlock } from "@/blocks/utils/types";
import type { PayloadBlockContext } from "@/utils/payload-block";
import type { PayloadBlockDataResolver } from "@/utils/payload-block-data-resolver/types";

type DefineBlockDataResolverArgs<T extends BaseBlock, R> = [
	slug: string,

	/**
	 * The function to execute for the data resolver.
	 */
	fn: (ctx: PayloadBlockContext, block: T) => Promise<R> | R,
];

/**
 * Defines a data resolver for a block.
 */
export const defineBlockDataResolver = <T extends BaseBlock, R>(
	...args: DefineBlockDataResolverArgs<T, R>
): PayloadBlockDataResolver<T, R> => {
	return {
		slug: args[0],
		fn: ({ ctx, block }) => args[1](ctx, block),
	};
};
