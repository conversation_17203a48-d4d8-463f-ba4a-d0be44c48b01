import "server-only";

import type { BaseBlock } from "@/blocks/utils/types";
import type { PayloadBlockContext } from "@/utils/payload-block";
import type { PayloadBlockDataResolver } from "@/utils/payload-block-data-resolver/types";
import type { FC } from "react";

/**
 * Defines an optional DataResolver for a block.
 */
type OptDataResolver<T extends BaseBlock> =
	| PayloadBlockDataResolver<T>
	| undefined;

/**
 * Defines the props for a block component.
 */
type Props<T extends BaseBlock, D extends OptDataResolver<T> = undefined> = {
	block: T;
	ctx: PayloadBlockContext;
} & (D extends PayloadBlockDataResolver<T, infer R> ? { data: R } : {});

/**
 * Defines a validation function for a block component.
 * This utilizes the same arguments as the component itself.
 */
type ValidateFn<T extends BaseBlock, D extends OptDataResolver<T>> = (
	args: Props<T, D>,
) => boolean;

type DefineBlockComponentArgs<
	T extends BaseBlock,
	D extends OptDataResolver<T>,
> = [
	/**
	 * The slug of the block.
	 */
	slug: string,

	/**
	 * The component for the block.
	 */
	component: FC<Props<T, D>>,

	/**
	 * Optional validation function for the block.
	 * If not provided, the component will always be rendered.
	 */
	validate?: ValidateFn<T, D>,
];

/**
 * Converts a string to PascalCase.
 *
 * @param input The input string.
 */
const toPascalCase = (input: string): string =>
	input
		.split("-")
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
		.join("");

export type InferDataResolverReturn<T extends OptDataResolver<any>> =
	T extends PayloadBlockDataResolver<any, infer R> ? R : undefined;

/**
 * Represents a block component.
 */
export interface BlockComponent<
	T extends BaseBlock = BaseBlock,
	D extends OptDataResolver<T> = undefined,
> {
	slug: string;
	component: FC<Props<T, D>>;
	validate?: ValidateFn<T, D>;
}

/**
 * Defines a block component.
 *
 * @param args
 */
export const defineBlockComponent = <
	T extends BaseBlock,
	D extends OptDataResolver<T> = undefined,
>(
	...args: DefineBlockComponentArgs<T, D>
): BlockComponent<T, D> => {
	const slug = args[0];
	const component = args[1];
	const validate = args[2];

	// Infer the display name of the component using the slug.
	Object.defineProperty(component, "displayName", {
		value: `${toPascalCase(slug)}BlockComponent`,
	});

	return {
		slug,
		component,
		validate,
	};
};

/**
 * Validates a block component. If the block component has a validate function,
 * it will be called. Otherwise, the component will always be rendered.
 *
 * @param blockComponent The block component to validate.
 * @param args The arguments to pass to the validate function.
 */
export const validateBlockComponent = <
	T extends BaseBlock,
	D extends OptDataResolver<T>,
>(
	blockComponent: BlockComponent<T, D>,
	args: Props<T, D>,
): boolean => {
	// If there is not validate function defined, we always assume the component is valid.
	if (!blockComponent.validate) {
		return true;
	}

	return blockComponent.validate(args);
};
