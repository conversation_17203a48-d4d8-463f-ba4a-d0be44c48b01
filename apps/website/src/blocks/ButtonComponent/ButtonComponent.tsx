import { defineBlockComponent } from "../utils";
import { CButtonLink } from "@/components/CButton";
import { resolvePayloadLink } from "@/utils/payload-block-utils";

import type { ButtonComponentBlock } from "@/gen/payload/types";

export const ButtonComponent = defineBlockComponent<ButtonComponentBlock>(
	"button-component",
	({ block, ctx }) => {
		return (
			<CButtonLink
				padding="extended"
				{...resolvePayloadLink({ ctx, link: block.link })}
			>
				{block.label}
			</CButtonLink>
		);
	},
);
