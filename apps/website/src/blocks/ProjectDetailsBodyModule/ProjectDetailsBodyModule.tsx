import { defineBlockComponent } from "@/blocks/utils";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { getPayloadDocument } from "@/utils/payload-block-utils";

import type { ProjectDetailsBodyModuleBlock } from "@/gen/payload/types";

export const ProjectDetailsBodyModule =
	defineBlockComponent<ProjectDetailsBodyModuleBlock>(
		"project-details-body-module",
		({ block, ctx }) => {
			const project = getPayloadDocument(ctx, "projects");

			return (
				<LModuleWrapper block={block}>
					<div className="md:columns-2 gap-16 prose prose-default max-w-full">
						{project.description && (
							<FLexicalRenderer content={project.description} />
						)}
					</div>
				</LModuleWrapper>
			);
		},
		// Do not render if there is no description.
		({ ctx }) => getPayloadDocument(ctx, "projects")?.description !== undefined,
	);
