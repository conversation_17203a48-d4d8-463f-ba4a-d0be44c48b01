import { defineBlockComponent } from "@/blocks/utils";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { MNumbersGrid, MNumbersGridItem } from "@/components/MNumbersGrid";

import type { NumbersGridModuleBlock } from "@/gen/payload/types";

export const NumbersGridModule = defineBlockComponent<NumbersGridModuleBlock>(
	"numbers-grid-module",
	({ block }) => {
		return (
			<LModuleWrapper block={block} size="small">
				<MNumbersGrid perRow={Number(block.perRow ?? "3")}>
					{block.items.map((item, idx) => (
						<MNumbersGridItem
							key={idx}
							number={item.number}
							title={item.prefix}
							description={item.suffix}
							decimals={item.decimals ?? 0}
						/>
					))}
				</MNumbersGrid>
			</LModuleWrapper>
		);
	},
);
