"use client";
import React, { useRef } from "react";
import CWorldMap from "react-svg-worldmap";
import { useResizeObserver } from "usehooks-ts";

import type { FC, CSSProperties, RefObject } from "react";
import type { CountryContext } from "react-svg-worldmap/dist/types";

interface Props {
	highlightedCountries?: string[];
}

export const WorldMap: FC<Props> = (props) => {
	const containerRef = useRef<HTMLDivElement>(
		null,
	) as RefObject<HTMLDivElement>;

	const { width = 0 } = useResizeObserver({
		ref: containerRef,
		box: "border-box",
	});

	const _styleFunction = (ctx: CountryContext): CSSProperties => {
		const isHighlighted =
			props.highlightedCountries?.includes(ctx.countryCode) ?? false;

		return {
			stroke: "white",
			strokeWidth: 1,
			...(isHighlighted
				? { fill: "#003259" }
				: { fill: "var(--color-meadow)" }),
		};
	};

	return (
		<div ref={containerRef} className="w-full aspect-[4/3]">
			<CWorldMap
				data={[]}
				size={width}
				styleFunction={_styleFunction}
				strokeOpacity={1}
				richInteraction={false}
				borderColor="#000"
			/>
		</div>
	);
};
