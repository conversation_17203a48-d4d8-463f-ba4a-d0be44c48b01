import { defineBlockComponent } from "@/blocks/utils";
import { WorldMap } from "@/blocks/WorldMapModule/components/WorldMap";
import { LModuleWrapper } from "@/components/LModuleWrapper";

import type { WorldMapModuleBlock } from "@/gen/payload/types";

export const WorldmapModule = defineBlockComponent<WorldMapModuleBlock>(
	"world-map-module",
	({ block }) => {
		return (
			<LModuleWrapper block={block} size="small">
				<WorldMap
					highlightedCountries={block.highlightedCountries ?? undefined}
				/>
			</LModuleWrapper>
		);
	},
);
