import { forwardRef } from "react";

import type { HTMLAttributes } from "react";

type Props = HTMLAttributes<SVGSVGElement>;

export const DownloadIcon = forwardRef<SVGSVGElement, Props>((props, ref) => {
	return (
		<svg
			{...props}
			ref={ref}
			xmlns="http://www.w3.org/2000/svg"
			viewBox="0 0 20 19"
		>
			<g fill="none" fillRule="evenodd" stroke="#000" strokeWidth="2">
				<path d="M19 12v6H1v-6M5 7l5 5 5-5M10 12V0" />
			</g>
		</svg>
	);
});
