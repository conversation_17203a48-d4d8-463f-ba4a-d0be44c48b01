import { DownloadIcon } from "@/blocks/DownloadModule/components/DownloadIcon";
import { defineBlockComponent } from "@/blocks/utils";
import { CButton } from "@/components/CButton";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { Link } from "@/navigation";

import type { DownloadModuleBlock } from "@/gen/payload/types";

export const DownloadModule = defineBlockComponent<DownloadModuleBlock>(
	"download-module",
	({ block }) => {
		return (
			<LModuleWrapper
				block={block}
				className="flex flex-col lg:flex-row gap-x-16 gap-y-12 items-end"
			>
				<div className="font-display text-title-section uppercase flex-2/3">
					<FLexicalRenderer content={block.title} firstParagraphOnly={true} />
				</div>

				<div className="flex-1/3 flex  gap-x-4">
					<DownloadIcon className="h-12" />
					<CButton size="extended" allowMultiLine={true}>
						<Link href="">{block.fileLabel}</Link>
					</CButton>
				</div>
			</LModuleWrapper>
		);
	},
);
