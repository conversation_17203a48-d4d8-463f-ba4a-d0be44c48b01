import { defineBlockComponent } from "@/blocks/utils";
import { FImage } from "@/components/FImage";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { MCardSlider } from "@/components/MCardSlider";
import { getPayloadDocument } from "@/utils/payload-block-utils";

import type { ProjectDetailsHeroModuleBlock } from "@/gen/payload/types";

export const ProjectDetailsImagesModule =
	defineBlockComponent<ProjectDetailsHeroModuleBlock>(
		"project-details-images-module",
		({ block, ctx }) => {
			const project = getPayloadDocument(ctx, "projects");

			if (!project.allImages || project.allImages.length === 0) {
				return null;
			}

			return (
				<LModuleWrapper block={block}>
					<MCardSlider singlePerView={true}>
						{project.allImages.map((element) => {
							if (typeof element !== "object") {
								return null;
							}

							return (
								<div
									key={element.id}
									className="aspect-[16/9] flex justify-center"
								>
									<FImage media={element as any} className="h-full w-auto" />
								</div>
							);
						})}
					</MCardSlider>
				</LModuleWrapper>
			);
		},
		// Do not render if there are no images.
		({ ctx }) =>
			(getPayloadDocument(ctx, "projects")?.allImages?.length ?? 0) > 0,
	);
