import payloadConfig from "@payload-config";
import { getPayload } from "payload";

import { defineBlockDataResolver } from "@/blocks/utils/block-data-resolver";
import { adjustLegacyData } from "@/utils/legacy";

export const JobsDocumentShellDataResolver = defineBlockDataResolver(
	"jobs-document-shell",
	async (ctx) => {
		const payload = await getPayload({ config: payloadConfig });

		const data = await payload.findGlobal({
			slug: "jobs-shell",
			locale: ctx.locale,
		});

		return adjustLegacyData(data);
	},
);
