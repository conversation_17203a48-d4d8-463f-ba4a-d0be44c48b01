import { defineBlockComponent } from "@/blocks/utils";
import { LPageShellLayout } from "@/components/LPageShellLayout";

import type { JobsDocumentShellDataResolver } from "@/blocks/JobsDocumentShell/JobsDocumentShellDataResolver";
import type { DocumentShellBlock } from "@/blocks/utils/types";
import type { Job } from "@/gen/payload/types";

export const JobsDocumentShell = defineBlockComponent<
	DocumentShellBlock<Job, "jobs">,
	typeof JobsDocumentShellDataResolver
>("jobs-document-shell", ({ ctx, data }) => (
	<LPageShellLayout data={data.layout} ctx={ctx} />
));
