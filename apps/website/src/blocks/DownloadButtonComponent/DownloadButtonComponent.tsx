import { defineBlockComponent } from "../utils";
import { DownloadIcon } from "@/blocks/DownloadModule/components/DownloadIcon";
import { CButton } from "@/components/CButton";

import type { DownloadButtonComponentBlock } from "@/gen/payload/types";

export const DownloadButtonComponent =
	defineBlockComponent<DownloadButtonComponentBlock>(
		"download-button-component",
		({ block }) => {
			let href = "";
			if (typeof block.file === "object") {
				href = block.file.url ?? "";
			}

			return (
				<div className="flex-1/3 flex  gap-x-4">
					<DownloadIcon className="h-12" />
					<CButton size="extended" allowMultiLine={true}>
						<a href={href} target="_blank">
							{block.label}
						</a>
					</CButton>
				</div>
			);
		},
	);
