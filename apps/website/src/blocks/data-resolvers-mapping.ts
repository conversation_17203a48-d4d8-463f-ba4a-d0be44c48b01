import { JobsDocumentShellDataResolver } from "@/blocks/JobsDocumentShell/JobsDocumentShellDataResolver";
import { JobsTableModuleDataResolver } from "@/blocks/JobsTableModule/JobsTableModuleDataResolver";
import { NewsDocumentShellDataResolver } from "@/blocks/NewsDocumentShell/NewsDocumentShellDataResolver";
import { NewsListModuleDataResolver } from "@/blocks/NewsListModule/NewsListModuleDataResolver";
import { PersonGridModuleDataResolver } from "@/blocks/PersonGridModule/PersonGridModuleDataResolver";
import { ProjectsDocumentShellDataResolver } from "@/blocks/ProjectsDocumentShell/ProjectsDocumentShellDataResolver";
import { ProjectsListModuleDataResolver } from "@/blocks/ProjectsListModule/ProjectsListModuleDataResolver";

import type { PayloadBlockDataResolver } from "@/utils/payload-block-data-resolver/types";

export interface IndexedBlockDataResolversMapping {
	[key: string]: PayloadBlockDataResolver<any>;
}

export const dataResolversMapping = [
	ProjectsListModuleDataResolver,
	PersonGridModuleDataResolver,
	NewsListModuleDataResolver,
	JobsTableModuleDataResolver,

	// - Document Shells
	ProjectsDocumentShellDataResolver,
	NewsDocumentShellDataResolver,
	JobsDocumentShellDataResolver,
];

/**
 * Creates an index of all blocks.
 * This is used to quickly find a block by its slug.
 */
export const createIndexBlockDataResolversMapping =
	(): IndexedBlockDataResolversMapping => {
		const mapping: IndexedBlockDataResolversMapping = {};

		dataResolversMapping.forEach((it) => {
			mapping[it.slug] = it as any;
		});

		return mapping;
	};
