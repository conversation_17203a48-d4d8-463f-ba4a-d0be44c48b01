import { ManagedModal } from "@/blocks/SicoreMigrationModal/components/ManagedModal";
import { defineBlockComponent } from "@/blocks/utils";
import { CSectionSeparator } from "@/components/CSectionSeparator";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";

import type { SicoreMigrationModalBlock } from "@/gen/payload/types";

export const SicoreMigrationModal =
	defineBlockComponent<SicoreMigrationModalBlock>(
		"sicore-migration-modal",
		({ block, ctx }) => {
			let isEnabled = false;

			if (!block.title || !block.body) {
				return null;
			}

			// If the utm_source is set to hansainvest-real.de, then the modal is enabled.
			if (ctx.searchParams["utm_source"] === "hansainvest-real.de") {
				isEnabled = true;
			}

			return isEnabled ? (
				<ManagedModal>
					<div className="flex flex-col md:flex-row gap-x-12">
						<div className="flex flex-col select-none">
							<div className="font-display font-bold uppercase text-5xl mb-3">
								<FLexicalRenderer content={block.title} />
							</div>
							<CSectionSeparator />
						</div>

						<div className="font-body md:text-right pt-12 text-balance hyphens-auto [&>p]:mb-4">
							{block.body && <FLexicalRenderer content={block.body} />}
						</div>
					</div>
				</ManagedModal>
			) : null;
		},
	);
