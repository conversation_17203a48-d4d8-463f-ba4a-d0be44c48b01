"use client";

import {
	Close<PERSON>utton,
	Dialog,
	DialogBackdrop,
	DialogPanel,
} from "@headlessui/react";
import clsx from "clsx";
import { X } from "lucide-react";
import { useEffect, useState } from "react";

import type { FC, ReactNode } from "react";

interface Props {
	children: ReactNode;
}

export const ManagedModal: FC<Props> = ({ children }) => {
	const [isOpen, setIsOpen] = useState(false);

	useEffect(() => {
		setTimeout(() => {
			setIsOpen(true);
		}, 250);
	}, []);

	return (
		<Dialog
			open={isOpen}
			transition
			className={clsx(
				"relative z-50",
				"transition duration-300 ease-out data-[closed]:opacity-0 data-[enter]:opacity-1",
			)}
			onClose={() => {
				setIsOpen(false);
			}}
		>
			<DialogBackdrop className="fixed inset-0 bg-black/20" />
			<div className="fixed inset-0 flex w-screen items-center justify-center p-4">
				<DialogPanel
					className={clsx(
						"max-w-5xl w-full space-y-4 my-gradient p-12 relative",
						"border-t-10 border-b-10 border-black",
					)}
				>
					<CloseButton
						className={clsx("absolute p-4 top-4 right-6 m-0 cursor-pointer")}
					>
						<X className="h-12 w-12" />
					</CloseButton>

					{children}
				</DialogPanel>
			</div>
		</Dialog>
	);
};
