import clsx from "clsx";
import { getFormatter } from "next-intl/server";

import { defineBlockComponent } from "@/blocks/utils";
import { CPagination } from "@/components/CPagination";
import { FImage } from "@/components/FImage";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { Link } from "@/navigation";
import { buildPayloadLink } from "@/utils/payload-block-utils/link";
import { getPayloadCollectionMapping } from "@/utils/payload-collections-mapping";

import type { NewsListModuleDataResolver } from "@/blocks/NewsListModule/NewsListModuleDataResolver";
import type { NewsListModuleBlock } from "@/gen/payload/types";

export const NewsListModule = defineBlockComponent<
	NewsListModuleBlock,
	typeof NewsListModuleDataResolver
>("news-list-module", async ({ block, ctx, data }) => {
	const collectionMapping = getPayloadCollectionMapping(ctx, "news");
	const format = await getFormatter();

	return (
		<LModuleWrapper block={block}>
			<div className={clsx("grid gap-16 mb-16", "grid-cols-4")}>
				{data.news.map((element) => {
					const formattedPublishDate = format.dateTime(
						new Date(element.publishDate),
						{
							year: "numeric",
							month: "2-digit",
							day: "2-digit",
						},
					);

					const translatedCategory = block.categoryTranslations?.find(
						(it) => it.key === element.category,
					)?.value;

					// Build the link to the project.
					const link = buildPayloadLink({
						ctx,
						slug: collectionMapping?.resolvers[ctx.locale].build({
							id: element.id,
							slug: element.slug,
						}),
					});

					return (
						<Link
							href={link}
							key={element.id}
							className={clsx("relative group flex flex-col gap-y-8")}
						>
							<FImage
								media={element.coverImage as any}
								className="w-full object-cover aspect-square"
								quality={85}
								sizes="(min-width: 640px) 50vw, (min-width: 1024px) 33vw, (min-width: 1440px) 480px, 100vw"
							/>
							<div>
								<h3 className="text-body font-bold uppercase text-2xl mb-4 line-clamp-3">
									{element.title}
								</h3>
								<p className="text-body-smaller leading-snug">
									{formattedPublishDate} {translatedCategory}
								</p>
							</div>
						</Link>
					);
				})}
			</div>

			<CPagination
				currentPage={data.pagination.current}
				totalPages={data.pagination.total}
				nextPageLink={
					data.pagination.next
						? buildPayloadLink({
								ctx,
								searchParams: { p: data.pagination.next },
							})
						: undefined
				}
				prevPageLink={
					data.pagination.previous
						? buildPayloadLink({
								ctx,
								searchParams: { p: data.pagination.previous },
							})
						: undefined
				}
			/>
		</LModuleWrapper>
	);
});
