import payloadConfig from "@payload-config";
import { getPayload } from "payload";

import { defineBlockDataResolver } from "@/blocks/utils/block-data-resolver";
import { buildPagination } from "@/utils/pagination";

import type { NewsListModuleBlock } from "@/gen/payload/types";
import type { Where } from "payload";

export const NewsListModuleDataResolver = defineBlockDataResolver(
	"news-list-module",
	async (ctx, block: NewsListModuleBlock) => {
		const payload = await getPayload({ config: payloadConfig });

		const perPage = block.pageSize;
		const currentPage = Math.max(ctx.searchParams.p ?? 1, 1);

		const whereCondition: Where = {
			...(!ctx.isPreview && { _status: { equals: "published" } }),
			publishDate: { less_than: new Date().toISOString() },
		};

		const totalNews = (
			await payload.count({
				collection: "news",
				// If we are not in preview mode, we only include published pages.
				where: whereCondition,
			})
		).totalDocs;

		const news = await payload.find({
			collection: "news",
			limit: perPage,
			pagination: true,
			page: currentPage,
			sort: "-publishDate",

			// If this is intended for preview mode, we include draft versions.
			draft: ctx.isPreview,

			// If we are not in preview mode, we only include published pages.
			where: whereCondition,
		});

		return {
			news: news.docs,
			pagination: buildPagination({
				currentPage,
				totalPages: Math.ceil(totalNews / perPage),
			}),
		};
	},
);
