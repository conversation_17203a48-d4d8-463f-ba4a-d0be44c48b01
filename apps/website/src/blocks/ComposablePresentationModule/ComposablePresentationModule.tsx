import clsx from "clsx";

import { defineBlockComponent } from "@/blocks/utils";
import { F<PERSON><PERSON><PERSON>enderer } from "@/components/FBlockRenderer";
import { FImage } from "@/components/FImage";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { LModuleWrapper } from "@/components/LModuleWrapper";

import type { ComposablePresentationModuleBlock } from "@/gen/payload/types";
import type { PayloadBlockContext } from "@/utils/payload-block";
import type { FC } from "react";

type RawBlockType =
	ComposablePresentationModuleBlock["columns"][number]["blockType"];

type BlockColumnType<T extends RawBlockType> = Extract<
	ComposablePresentationModuleBlock["columns"][number],
	{ blockType: T }
>;

interface ColumnProps<T extends RawBlockType> {
	column: BlockColumnType<T>;
	ctx: PayloadBlockContext;
}

const ColumnImage: FC<ColumnProps<"image">> = (props) => {
	return (
		<div className="aspect-[16/12] w-full overflow-hidden">
			<FImage
				media={props.column.image as any}
				className="h-full w-full"
				quality={80}
				sizes="(min-width: 1440px) 720px, 50vw"
				loading="lazy"
			/>
		</div>
	);
};

const ColumnImageIcon: FC<ColumnProps<"image-icon">> = (props) => {
	return (
		<div className="aspect-[16/12] w-full overflow-hidden bg-highlighted flex justify-center items-center h-full p-8">
			<FImage
				media={props.column.icon as any}
				className={clsx(
					props.column.size === "contained-default" && "max-h-96 h-full w-auto",
				)}
				quality={80}
				sizes="(min-width: 1440px) 720px, 50vw"
				loading="lazy"
			/>
		</div>
	);
};

const ColumnTitleBody: FC<ColumnProps<"title-body">> = ({ column, ctx }) => {
	return (
		<div className={clsx("h-full flex flex-col gap-8 justify-between")}>
			<h1 className="font-display uppercase text-title-section hyphens-auto text-balance">
				<FLexicalRenderer content={column.title} firstParagraphOnly={true} />
			</h1>

			<div className="flex flex-col gap-y-6">
				<div className="prose prose-default max-w-full">
					<FLexicalRenderer content={column.body} />
				</div>
				{column.actions && column.actions.length > 0 && (
					<div>
						{column.actions.map((action, idx) => (
							<FBlockRenderer block={action} key={idx} ctx={ctx} />
						))}
					</div>
				)}
			</div>
		</div>
	);
};

const ColumnTitle: FC<ColumnProps<"title">> = ({ column }) => {
	return (
		<h1 className="font-display uppercase text-title-section hyphens-auto text-balance">
			<FLexicalRenderer content={column.title} firstParagraphOnly={true} />
		</h1>
	);
};

const ColumnTitleIcon: FC<ColumnProps<"title-icon">> = ({ column }) => {
	return (
		<div className={clsx("h-full flex flex-col gap-8 justify-between")}>
			<h1 className="font-display uppercase text-title-section hyphens-auto text-balance">
				<FLexicalRenderer content={column.title} firstParagraphOnly={true} />
			</h1>

			<div className="w-full hidden lg:flex justify-center items-center h-full">
				<FImage
					media={column.icon as any}
					className={clsx("max-h-48 h-full w-auto")}
					quality={80}
					sizes="(min-width: 1440px) 720px, 50vw"
					loading="lazy"
				/>
			</div>
		</div>
	);
};

const ColumnBody: FC<ColumnProps<"body">> = ({ column }) => {
	return (
		<div className="prose prose-default max-w-full">
			<FLexicalRenderer content={column.body} />
		</div>
	);
};

export const ComposablePresentationModule =
	defineBlockComponent<ComposablePresentationModuleBlock>(
		"composable-presentation-module",
		({ block, ctx }) => {
			return (
				<LModuleWrapper block={block}>
					<div className={clsx("relative isolate w-full")}>
						<div
							className={clsx(
								"relative z-10",
								"grid gap-x-16 gap-y-12",

								"grid-cols-1",

								block.spanSingleColumn && block.columns.length === 1
									? "lg:grid-cols-1"
									: "lg:grid-cols-2",
							)}
						>
							{block.columns.map((column, idx) => (
								<div key={idx} className={clsx("")}>
									{column.blockType === "image" && (
										<ColumnImage column={column} ctx={ctx} />
									)}

									{column.blockType === "image-icon" && (
										<ColumnImageIcon column={column} ctx={ctx} />
									)}

									{column.blockType === "title-body" && (
										<ColumnTitleBody column={column} ctx={ctx} />
									)}

									{column.blockType === "title" && (
										<ColumnTitle column={column} ctx={ctx} />
									)}

									{column.blockType === "title-icon" && (
										<ColumnTitleIcon column={column} ctx={ctx} />
									)}

									{column.blockType === "body" && (
										<ColumnBody column={column} ctx={ctx} />
									)}
								</div>
							))}
						</div>
					</div>
				</LModuleWrapper>
			);
		},
	);
