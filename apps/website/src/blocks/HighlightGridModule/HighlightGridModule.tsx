import clsx from "clsx";
import * as motion from "motion/react-client";

import { defineBlockComponent } from "../utils";
import { FImage } from "@/components/FImage";
import { FLexicalRenderer } from "@/components/FLexicalRenderer";
import { FPayloadLink } from "@/components/FPayloadLink";
import { LModuleWrapper } from "@/components/LModuleWrapper";
import { isAvailablePayloadLink } from "@/utils/payload-block-utils";

import type { HighlightGridModuleBlock } from "@/gen/payload/types";

export const HighlightGridModule =
	defineBlockComponent<HighlightGridModuleBlock>(
		"highlight-grid-module",
		({ block, ctx }) => {
			return (
				<LModuleWrapper block={block} size="small">
					<motion.div
						className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-12"
						viewport={{ once: true }}
						initial="hidden"
						whileInView="visible"
						variants={{
							visible: {
								opacity: 1,
								transition: {
									when: "beforeChildren",
									staggerChildren: 0.15,
								},
							},
						}}
					>
						{block.items.map((item, idx) => (
							<motion.div
								key={idx}
								variants={{
									hidden: {
										opacity: 0,
										y: 20,
										filter: "blur(3px)",
									},
									visible: {
										opacity: 1,
										y: 0,
										filter: "blur(0px)",
										transition: {
											duration: 0.5,
										},
									},
								}}
								className={clsx(
									"flex flex-col",
									"bg-highlighted",
									"p-6 aspect-square relative group",
								)}
							>
								<div className="flex flex-col gap-y-8 items-center">
									<FImage media={item.image as any} className="h-32 w-32" />

									<div className="bg-black h-2 w-16" />

									<div className="text-2xl font-display text-center line-clamp-2">
										{item.title && <FLexicalRenderer content={item.title} />}
									</div>
								</div>

								<div
									className={clsx(
										"absolute inset-0 bg-black opacity-0 pointer-events-none",
										"group-hover:opacity-100 group-hover:pointer-events-auto",
										"transition-opacity duration-200 ease-in-out",
										"p-6",
									)}
								>
									{item.body && (
										<div className="text-white text-xl">
											<span>
												<FLexicalRenderer
													content={item.body}
													firstParagraphOnly={true}
												/>
											</span>
											{isAvailablePayloadLink({
												link: item.link,
												withLabel: true,
											}) && (
												<>
													{" "}
													<span
														className={clsx(
															"underline uppercase font-bold font-display",
														)}
													>
														<FPayloadLink
															ctx={ctx}
															link={item.link}
															useLabel={true}
														/>
													</span>
												</>
											)}
										</div>
									)}
								</div>
							</motion.div>
						))}
					</motion.div>
				</LModuleWrapper>
			);
		},
	);
