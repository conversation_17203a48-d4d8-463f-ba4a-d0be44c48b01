import { Footer } from "./Footer";
import { GlobalReference } from "./GlobalReference";
import { Header } from "./Header";
import { HeroModule } from "./HeroModule";
import { NumbersGridModule } from "./NumbersGridModule";
import { PresentationDualModule } from "./PresentationDualModule";
import { PresentationModule } from "./PresentationModule";
import { SectionHeaderModule } from "./SectionHeaderModule";
import { SectionWrapper } from "./SectionWrapper";
import { ButtonComponent } from "@/blocks/ButtonComponent";
import { ComposablePresentationModule } from "@/blocks/ComposablePresentationModule";
import { ContactBannerModule } from "@/blocks/ContactBannerModule";
import { DownloadButtonComponent } from "@/blocks/DownloadButtonComponent/DownloadButtonComponent";
import { DownloadModule } from "@/blocks/DownloadModule";
import { DynamicGraphicsModule } from "@/blocks/DynamicGraphicsModule";
import { HighlightGridModule } from "@/blocks/HighlightGridModule";
import { IntroModule } from "@/blocks/IntroModule";
import { JobDetailsBodyModule } from "@/blocks/JobDetailsBodyModule/JobDetailsBodyModule";
import { JobDetailsHeroModule } from "@/blocks/JobDetailsHeroModule/JobDetailsHeroModule";
import { JobsDocumentShell } from "@/blocks/JobsDocumentShell/JobsDocumentShell";
import { JobsTableModule } from "@/blocks/JobsTableModule/JobsTableModule";
import { NewsDetailsBodyModule } from "@/blocks/NewsDetailsBodyModule/NewsDetailsBodyModule";
import { NewsDetailsHeroModule } from "@/blocks/NewsDetailsHeroModule/NewsDetailsHeroModule";
import { NewsDocumentShell } from "@/blocks/NewsDocumentShell/NewsDocumentShell";
import { NewsListModule } from "@/blocks/NewsListModule/NewsListModule";
import { PageHeaderModule } from "@/blocks/PageHeaderModule";
import { PagesDocumentShell } from "@/blocks/PagesDocumentShell/PagesDocumentShell";
import { PersonGridModule } from "@/blocks/PersonGridModule/PersonGridModule";
import { PresentationItemsModule } from "@/blocks/PresentationItemsModule";
import { ProjectDetailsBodyModule } from "@/blocks/ProjectDetailsBodyModule/ProjectDetailsBodyModule";
import { ProjectDetailsFactsModule } from "@/blocks/ProjectDetailsFactsModule/ProjectDetailsFactsModule";
import { ProjectDetailsHeroModule } from "@/blocks/ProjectDetailsHeroModule/ProjectDetailsHeroModule";
import { ProjectDetailsImagesModule } from "@/blocks/ProjectDetailsImagesModule/ProjectDetailsImagesModule";
import { ProjectsDocumentShell } from "@/blocks/ProjectsDocumentShell/ProjectsDocumentShell";
import { ProjectsListModule } from "@/blocks/ProjectsListModule/ProjectsListModule";
import { ProjectsSliderModule } from "@/blocks/ProjectsSliderModule";
import { SicoreMigrationModal } from "@/blocks/SicoreMigrationModal";
import { TypographyShowcaseModule } from "@/blocks/TypographyShowcaseModule";
import { ValuesGridModule } from "@/blocks/ValuesGridModuleBlock/ValuesGridModuleBlock";
import { WorldmapModule } from "@/blocks/WorldMapModule";

import type { BlockComponent } from "@/blocks/utils";

export interface IndexedBlockComponentsMapping {
	[key: string]: BlockComponent<any, any>;
}

export const componentsMapping = [
	// - Special blocks
	Header,
	Footer,
	GlobalReference,
	SectionWrapper,
	SicoreMigrationModal,

	// - Modules
	HeroModule,
	NumbersGridModule,
	SectionHeaderModule,
	PresentationModule,
	IntroModule,
	HighlightGridModule,
	PresentationDualModule,
	ProjectsSliderModule,
	ContactBannerModule,
	PageHeaderModule,
	TypographyShowcaseModule,
	PresentationItemsModule,
	ComposablePresentationModule,
	DownloadModule,
	WorldmapModule,
	ProjectsListModule,
	PersonGridModule,
	DynamicGraphicsModule,
	ValuesGridModule,
	NewsListModule,
	JobsTableModule,
	JobDetailsHeroModule,
	JobDetailsBodyModule,

	ProjectDetailsHeroModule,
	ProjectDetailsImagesModule,
	ProjectDetailsFactsModule,
	ProjectDetailsBodyModule,

	NewsDetailsHeroModule,
	NewsDetailsBodyModule,

	// - Components
	ButtonComponent,
	DownloadButtonComponent,

	// - Document Shells
	PagesDocumentShell,
	ProjectsDocumentShell,
	NewsDocumentShell,
	JobsDocumentShell,
];

/**
 * Creates an index of all blocks.
 * This is used to quickly find a block by its slug.
 */
export const createIndexBlockComponentsMapping =
	(): IndexedBlockComponentsMapping => {
		const mapping: IndexedBlockComponentsMapping = {};

		componentsMapping.forEach((it) => {
			mapping[it.slug] = it as any;
		});

		return mapping;
	};
